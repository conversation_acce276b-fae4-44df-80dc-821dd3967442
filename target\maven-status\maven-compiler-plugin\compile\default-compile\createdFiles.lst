com\aionemu\gameserver\model\templates\ai\BombTemplate.class
com\aionemu\gameserver\model\templates\siegelocation\SiegeRelatedBases.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BUILDER_CONTROL.class
com\aionemu\gameserver\model\templates\item\WeaponType.class
com\aionemu\gameserver\model\team\group\events\PlayerGroupUpdateEvent.class
com\aionemu\gameserver\model\team\legion\LegionWarehouse.class
com\aionemu\gameserver\skillengine\effect\AbsoluteSlowEffect.class
com\aionemu\gameserver\model\drop\Drop.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ABYSS_RANK_UPDATE.class
com\aionemu\gameserver\skillengine\effect\TargetChangeEffect.class
com\aionemu\gameserver\model\templates\portal\PortalUse.class
com\aionemu\gameserver\configs\main\RankingConfig.class
com\aionemu\gameserver\model\legionDominion\LegionDominionParticipantInfo.class
com\aionemu\gameserver\taskmanager\tasks\TeamStatUpdater$SingletonHolder.class
com\aionemu\gameserver\model\templates\rewards\ArenaRewardItem.class
com\aionemu\gameserver\network\aion\clientpackets\CM_TUNE_RESULT.class
com\aionemu\gameserver\model\trade\ExchangeItem.class
com\aionemu\gameserver\skillengine\effect\BuffStunEffect.class
com\aionemu\gameserver\dataholders\WalkerData.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DELETE_HOUSE_OBJECT.class
com\aionemu\gameserver\taskmanager\tasks\MovementNotifyTask.class
com\aionemu\gameserver\model\gameobjects\JukeBoxObject.class
com\aionemu\gameserver\network\aion\serverpackets\SM_UNWRAP_ITEM.class
com\aionemu\gameserver\model\gameobjects\BrokerItem$6.class
com\aionemu\gameserver\skillengine\effect\SnareEffect.class
com\aionemu\gameserver\model\gameobjects\player\Rates$1.class
com\aionemu\gameserver\model\gameobjects\UseableHouseObject.class
com\aionemu\gameserver\model\templates\event\BuffRestriction.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_LS_CONTROL_RESPONSE.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CRAFT_UPDATE.class
com\aionemu\gameserver\model\PlayerClass$PlayerStatsTemplate.class
com\aionemu\gameserver\model\templates\bounty\BountyType.class
com\aionemu\gameserver\model\gameobjects\LetterType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PLAYER_SPAWN.class
com\aionemu\gameserver\model\stats\calc\Stat2.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropWorld.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MAY_LOGIN_INTO_GAME.class
com\aionemu\gameserver\dao\PlayerCooldownsDAO$1.class
com\aionemu\gameserver\model\templates\event\Buff.class
com\aionemu\gameserver\model\templates\VisibleObjectTemplate.class
com\aionemu\gameserver\skillengine\effect\RebirthEffect.class
com\aionemu\gameserver\services\TradeService$1.class
com\aionemu\gameserver\services\mail\AbyssSiegeLevel.class
com\aionemu\gameserver\dataholders\StaticDoorData.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BUY_TRADE_IN_TRADE.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_GS_CHARACTER_RESPONSE.class
com\aionemu\gameserver\model\templates\zone\Point2D.class
com\aionemu\gameserver\model\templates\item\actions\DecomposeAction.class
com\aionemu\gameserver\model\gameobjects\BrokerItem$1.class
com\aionemu\gameserver\model\team\league\events\LeagueLeftEvent.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$9.class
com\aionemu\gameserver\model\items\RandomBonusEffect.class
com\aionemu\gameserver\dataholders\WorldMapsData.class
com\aionemu\gameserver\spawnengine\WalkerGroupShift.class
com\aionemu\gameserver\dataholders\Portal2Data.class
com\aionemu\gameserver\services\BaseService$1.class
com\aionemu\gameserver\ai\AIEngine$SingletonHolder.class
com\aionemu\gameserver\model\templates\mail\MailTemplate.class
com\aionemu\gameserver\geoEngine\bounding\BoundingBox$1.class
com\aionemu\gameserver\dao\PlayerNpcFactionsDAO$1.class
com\aionemu\gameserver\services\mail\MailService$1.class
com\aionemu\gameserver\dataholders\ItemPurificationData.class
com\aionemu\gameserver\skillengine\effect\ProcDPHealInstantEffect.class
com\aionemu\gameserver\model\team\common\events\ChangeLeaderEvent.class
com\aionemu\gameserver\dataholders\CustomDrop.class
com\aionemu\gameserver\model\templates\zone\Semisphere.class
com\aionemu\gameserver\model\templates\itemgroups\CraftItemGroup.class
com\aionemu\gameserver\spawnengine\StaticObjectSpawnManager.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CUSTOM_SETTINGS.class
com\aionemu\gameserver\skillengine\effect\HealCastorOnTargetDeadEffect.class
com\aionemu\gameserver\model\base\BaseOccupier$1.class
com\aionemu\gameserver\model\gameobjects\SummonedHouseNpc.class
com\aionemu\gameserver\dataholders\DataManager.class
com\aionemu\gameserver\model\stats\container\TrapGameStats$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HEADING_UPDATE.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SUMMON_EMOTION.class
com\aionemu\gameserver\dao\LegionDAO$5.class
com\aionemu\gameserver\configs\main\SiegeConfig.class
com\aionemu\gameserver\model\gameobjects\DropNpc.class
com\aionemu\gameserver\skillengine\condition\AbnormalStateCondition.class
com\aionemu\gameserver\dataholders\ConquerorAndProtectorData.class
com\aionemu\gameserver\model\gameobjects\Persistable.class
com\aionemu\gameserver\skillengine\condition\ChargeArmorCondition.class
com\aionemu\gameserver\services\CronJobService.class
com\aionemu\gameserver\configs\main\PunishmentConfig.class
com\aionemu\gameserver\questEngine\handlers\models\ItemCollectingData.class
com\aionemu\gameserver\services\LegionService$2.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$10.class
com\aionemu\gameserver\dataholders\CubeExpandData.class
com\aionemu\gameserver\model\siege\SiegeLocation.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropTribe.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalRule$RestrictionRace.class
com\aionemu\gameserver\model\team\common\events\AbstractTeamPlayerEvent.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LOOT_STATUS.class
com\aionemu\gameserver\model\gameobjects\player\Rates$6.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$31.class
com\aionemu\gameserver\model\items\ManaStone.class
com\aionemu\gameserver\model\trade\TradePSItem.class
com\aionemu\gameserver\skillengine\effect\SummonTrapEffect.class
com\aionemu\gameserver\model\team\common\service\PlayerTeamDistributionService$PlayerTeamRewardStats.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElementType$7.class
com\aionemu\gameserver\taskmanager\tasks\MoveTaskManager$SingletonHolder.class
com\aionemu\gameserver\skillengine\periodicaction\PeriodicAction.class
com\aionemu\gameserver\dataholders\PlayerInitialData$PlayerCreationData$ItemsType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_STATUPDATE_HP.class
com\aionemu\gameserver\dao\PlayerBindPointDAO.class
com\aionemu\gameserver\model\templates\item\actions\UseTarget.class
com\aionemu\gameserver\skillengine\effect\BoostDropRateEffect.class
com\aionemu\gameserver\world\zone\handler\QuestZoneHandler.class
com\aionemu\gameserver\dao\AbyssRankDAO$RankingListPlayer.class
com\aionemu\gameserver\model\templates\item\actions\AdoptPetAction.class
com\aionemu\gameserver\network\chatserver\clientpackets\CM_CS_AUTH_RESPONSE.class
com\aionemu\gameserver\utils\ThreadPoolManager.class
com\aionemu\gameserver\ai\HpPhases.class
com\aionemu\gameserver\skillengine\effect\ParalyzeEffect.class
com\aionemu\gameserver\skillengine\effect\modifier\AbnormalDamageModifier.class
com\aionemu\gameserver\taskmanager\AbstractCronTask.class
com\aionemu\gameserver\dataholders\RecipeData.class
com\aionemu\gameserver\model\templates\spawns\mercenaries\MercenaryZone.class
com\aionemu\gameserver\services\item\ItemService.class
com\aionemu\gameserver\model\templates\materials\MaterialTemplate.class
com\aionemu\gameserver\model\templates\spawns\riftspawns\RiftSpawnTemplate.class
com\aionemu\gameserver\model\templates\spawns\siegespawns\SiegeSpawnTemplate.class
com\aionemu\gameserver\services\OneVsOneService$1.class
com\aionemu\gameserver\model\gameobjects\state\CreatureSeeState.class
com\aionemu\gameserver\services\PvpService.class
com\aionemu\gameserver\utils\annotations\AnnotatedClass.class
com\aionemu\gameserver\network\aion\iteminfo\PlumeInfoBlobEntry.class
com\aionemu\gameserver\model\templates\materials\MaterialSkill.class
com\aionemu\gameserver\ai\AttackIntention.class
com\aionemu\gameserver\configs\schedule\WorldRaidSchedules.class
com\aionemu\gameserver\skillengine\effect\SummonHomingEffect.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$PoppySnackTastyGroup.class
com\aionemu\gameserver\model\autogroup\AutoGroup.class
com\aionemu\gameserver\configs\main\CleaningConfig.class
com\aionemu\gameserver\model\gameobjects\player\BlockedPlayer.class
com\aionemu\gameserver\model\templates\item\purification\PurificationResult.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_LS_CONTROL.class
com\aionemu\gameserver\skillengine\effect\AbsoluteSnareEffect.class
com\aionemu\gameserver\model\templates\challenge\ChallengeTaskTemplate.class
com\aionemu\gameserver\network\BannedMacManager.class
com\aionemu\gameserver\geoEngine\scene\Node.class
com\aionemu\gameserver\model\account\CharacterBanInfo.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHAT_GROUP_INFO.class
com\aionemu\gameserver\network\aion\clientpackets\CM_TELEPORT_ANIMATION_DONE.class
com\aionemu\gameserver\model\templates\assemblednpc\AssembledNpcTemplate.class
com\aionemu\gameserver\model\DuelResult.class
com\aionemu\gameserver\skillengine\effect\ProvokerEffect$2.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MEGAPHONE$FactionLabel.class
com\aionemu\gameserver\model\templates\materials\MaterialTarget.class
com\aionemu\gameserver\model\gameobjects\player\PetList.class
com\aionemu\gameserver\model\team\league\events\LeagueKinahDistributionEvent.class
com\aionemu\gameserver\model\Gender.class
com\aionemu\gameserver\model\templates\zone\ZoneInfo.class
com\aionemu\gameserver\services\item\ItemPacketService$ItemAddType.class
com\aionemu\gameserver\model\geometry\AbstractArea.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_ACCOUNT_LIST.class
com\aionemu\gameserver\skillengine\model\Effect$1.class
com\aionemu\gameserver\model\enchants\TemperingList.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PET.class
com\aionemu\gameserver\services\item\ItemPacketService$ItemUpdateType.class
com\aionemu\gameserver\utils\xml\XmlUtil.class
com\aionemu\gameserver\model\templates\item\actions\FireworksUseAction.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEVEL_READY.class
com\aionemu\gameserver\GameServerError.class
com\aionemu\gameserver\network\aion\clientpackets\AbstractGmCommandPacket.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CHAT_INIT.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_MACBAN_CONTROL.class
com\aionemu\gameserver\geoEngine\models\GeoMap$1.class
com\aionemu\gameserver\network\aion\instanceinfo\DredgionScoreWriter.class
com\aionemu\gameserver\GameServer$1.class
com\aionemu\gameserver\services\abyss\AbyssRankUpdateService.class
com\aionemu\gameserver\network\aion\clientpackets\CM_RECIPE_DELETE.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHECK_PAK.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$4.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropNpcNames.class
com\aionemu\gameserver\model\templates\mail\StringParamList$Param.class
com\aionemu\gameserver\services\StaticDoorService$SingletonHolder.class
com\aionemu\gameserver\skillengine\effect\FearEffect$1.class
com\aionemu\gameserver\dao\MailDAO$3.class
com\aionemu\gameserver\model\templates\spawns\siegespawns\SiegeSpawn$SiegeRaceTemplate$SiegeModTemplate.class
com\aionemu\gameserver\world\zone\PvPZoneInstance.class
com\aionemu\gameserver\model\templates\npc\SubDialogType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DUEL.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropExcludedNpcs.class
com\aionemu\gameserver\dataholders\PlayerInitialData$1.class
com\aionemu\gameserver\model\templates\npc\AbyssNpcType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElementType$2.class
com\aionemu\gameserver\network\aion\serverpackets\SM_L2AUTH_LOGIN_CHECK.class
com\aionemu\gameserver\skillengine\effect\ReturnEffect.class
com\aionemu\gameserver\network\BannedMacEntry.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\events\OnTalkEvent.class
com\aionemu\gameserver\model\instance\instancescore\PvpInstanceScore.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHARACTER_LIST.class
com\aionemu\gameserver\model\templates\ingameshop\IGSubCategory.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LOOT_STATUS$Status.class
com\aionemu\gameserver\configs\main\GSConfig.class
com\aionemu\gameserver\skillengine\effect\ExtendAuraRangeEffect.class
com\aionemu\gameserver\controllers\attack\AttackUtil.class
com\aionemu\gameserver\dataholders\PanelSkillsData.class
com\aionemu\gameserver\model\templates\item\ExtraInventory.class
com\aionemu\gameserver\model\templates\world\AiInfo.class
com\aionemu\gameserver\services\rift\RiftManager.class
com\aionemu\gameserver\services\siege\SiegeCounter.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropRaces.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TELEPORT_MAP.class
com\aionemu\gameserver\dao\BrokerDAO.class
com\aionemu\gameserver\model\templates\StorageExpansionTemplate.class
com\aionemu\gameserver\taskmanager\tasks\LegionDominionIntruderUpdateTask$SingletonHolder.class
com\aionemu\gameserver\model\base\Base$1.class
com\aionemu\gameserver\model\trade\RepurchaseList.class
com\aionemu\gameserver\controllers\GatherableController.class
com\aionemu\gameserver\model\templates\item\actions\TamperingAction$2.class
com\aionemu\gameserver\services\mail\MailFormatter$4.class
com\aionemu\gameserver\services\CubeExpandService$1.class
com\aionemu\gameserver\model\broker\filter\BrokerMinMaxFilter.class
com\aionemu\gameserver\model\templates\windstreams\StreamLocations.class
com\aionemu\gameserver\skillengine\effect\SummonTotemEffect.class
com\aionemu\gameserver\geoEngine\collision\bih\BIHNode.class
com\aionemu\gameserver\services\item\ItemActionService.class
com\aionemu\gameserver\skillengine\effect\SummonOwner.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$FeedExcludeGroup.class
com\aionemu\gameserver\restrictions\PlayerRestrictions.class
com\aionemu\gameserver\spawnengine\InstanceWalkerFormations.class
com\aionemu\gameserver\model\templates\item\actions\RideAction$3.class
com\aionemu\gameserver\services\WarehouseService$1.class
com\aionemu\gameserver\skillengine\task\CraftingTask$1.class
com\aionemu\gameserver\questEngine\handlers\template\RelicRewards.class
com\aionemu\gameserver\services\instance\InstanceService$EmptyInstanceCheckerTask.class
com\aionemu\gameserver\controllers\VisibleObjectController.class
com\aionemu\gameserver\custom\instance\neuralnetwork\PlayerModelEntry.class
com\aionemu\gameserver\dao\LegionMemberDAO$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HOUSE_TELEPORT.class
com\aionemu\gameserver\services\reward\VeteranRewardService.class
com\aionemu\gameserver\custom\instance\neuralnetwork\PlayerModelController.class
com\aionemu\gameserver\ai\event\AIEventType.class
com\aionemu\gameserver\dataholders\HotspotData.class
com\aionemu\gameserver\model\templates\quest\QuestDrop.class
com\aionemu\gameserver\skillengine\model\HopType.class
com\aionemu\gameserver\spawnengine\WalkerGroup.class
com\aionemu\gameserver\model\onevsone\OneVsOneParticipant.class
com\aionemu\gameserver\model\templates\item\enums\ItemSubType.class
com\aionemu\gameserver\services\siege\SiegeStartRunnable.class
com\aionemu\gameserver\model\team\group\events\PlayerGroupStopMentoringEvent.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropRatings.class
com\aionemu\gameserver\model\templates\item\actions\DecomposeAction$3.class
com\aionemu\gameserver\utils\xml\XmlValidationHandler.class
com\aionemu\gameserver\skillengine\condition\ChargeCondition.class
com\aionemu\gameserver\custom\instance\RoahCustomInstanceHandler$1.class
com\aionemu\gameserver\model\gameobjects\player\npcFaction\NpcFaction$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_QUEST_COMPLETED_LIST.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\CollectItemQuestOperation.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RIFT_ANNOUNCE.class
com\aionemu\gameserver\model\instance\DredgionRoom.class
com\aionemu\gameserver\model\templates\item\actions\ToyPetSpawnAction.class
com\aionemu\gameserver\skillengine\effect\BufEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EXCHANGE_ADD_KINAH.class
com\aionemu\gameserver\network\aion\serverpackets\SM_NICKNAME_CHECK_RESPONSE.class
com\aionemu\gameserver\ai\handler\FollowEventHandler.class
com\aionemu\gameserver\utils\stats\DropRewardEnum.class
com\aionemu\gameserver\model\skill\NpcSkillTemplateEntry.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\TakeItemOperation.class
com\aionemu\gameserver\skillengine\effect\AlwaysDodgeEffect$1.class
com\aionemu\gameserver\custom\instance\CustomInstanceRankEnum.class
com\aionemu\gameserver\model\gameobjects\PetEmote.class
com\aionemu\gameserver\model\templates\item\enums\ArmorType.class
com\aionemu\gameserver\services\PrivateStoreService.class
com\aionemu\gameserver\services\vortex\DimensionalVortex.class
com\aionemu\gameserver\model\templates\mail\Mails.class
com\aionemu\gameserver\services\QuestService.class
com\aionemu\gameserver\network\aion\instanceinfo\DarkPoetaScoreWriter.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_ACCOUNT_RECONNECT_KEY.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_EDIT.class
com\aionemu\gameserver\skillengine\properties\Properties.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MACRO_DELETE.class
com\aionemu\gameserver\model\broker\filter\BrokerPlayerClassExtraFilter.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$27.class
com\aionemu\gameserver\world\WorldPosition.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_UPDATE.class
com\aionemu\gameserver\model\gameobjects\state\FlyState.class
com\aionemu\gameserver\model\templates\walker\WalkerTemplate$LoopType.class
com\aionemu\gameserver\model\items\ChargeInfo.class
com\aionemu\gameserver\model\animations\ObjectDeleteAnimation.class
com\aionemu\gameserver\network\aion\clientpackets\CM_VIEW_PLAYER_DETAILS.class
com\aionemu\gameserver\skillengine\effect\ProcHealInstantEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EXCHANGE_LOCK.class
com\aionemu\gameserver\services\DuelService$1.class
com\aionemu\gameserver\model\base\BaseType.class
com\aionemu\gameserver\services\CommandsAccessService.class
com\aionemu\gameserver\dao\PlayerMacrosDAO$1.class
com\aionemu\gameserver\dataholders\QuestsData.class
com\aionemu\gameserver\model\stats\container\SummonLifeStats.class
com\aionemu\gameserver\skillengine\effect\AlwaysParryEffect.class
com\aionemu\gameserver\dao\CustomInstanceDAO.class
com\aionemu\gameserver\skillengine\effect\ProcVPHealInstantEffect.class
com\aionemu\gameserver\model\gameobjects\player\DeniedStatus.class
com\aionemu\gameserver\services\trade\PricesService.class
com\aionemu\gameserver\network\aion\instanceinfo\EternalBastionScoreWriter.class
com\aionemu\gameserver\dao\HeadhuntingDAO$2.class
com\aionemu\gameserver\ai\handler\ShoutEventHandler.class
com\aionemu\gameserver\skillengine\effect\HealInstantEffect.class
com\aionemu\gameserver\world\geo\GeoService$SingletonHolder.class
com\aionemu\gameserver\skillengine\effect\FearEffect.class
com\aionemu\gameserver\services\EnchantService.class
com\aionemu\gameserver\world\zone\handler\ZoneNameAnnotation.class
com\aionemu\gameserver\model\templates\item\actions\CompositionAction$2.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$15.class
com\aionemu\gameserver\network\aion\clientpackets\CM_COMPOSITE_STONES.class
com\aionemu\gameserver\network\aion\serverpackets\SM_INFLUENCE_RATIO.class
com\aionemu\gameserver\dataholders\GlobalNpcExclusionData.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_PREMIUM_CONTROL.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\QuestStatusCondition.class
com\aionemu\gameserver\model\flyring\FlyRing.class
com\aionemu\gameserver\model\templates\item\ReturnLocList.class
com\aionemu\gameserver\geoEngine\collision\IgnoreProperties.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHARACTER_PASSKEY.class
com\aionemu\gameserver\model\templates\pet\PetFunctionType.class
com\aionemu\gameserver\model\account\PlayerAccountData$VisibleItem.class
com\aionemu\gameserver\model\stats\container\CreatureGameStats.class
com\aionemu\gameserver\model\team\legion\LegionHistoryAction$Type.class
com\aionemu\gameserver\model\base\PanesterraBase.class
com\aionemu\gameserver\skillengine\effect\HideEffect.class
com\aionemu\gameserver\world\zone\SiegeZoneInstance.class
com\aionemu\gameserver\skillengine\effect\OneTimeBoostSkillCriticalEffect.class
com\aionemu\gameserver\questEngine\handlers\models\XMLQuest.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BROKER_SELL_WINDOW.class
com\aionemu\gameserver\network\aion\clientpackets\CM_REVIVE$1.class
com\aionemu\gameserver\model\templates\item\AssemblyItem.class
com\aionemu\gameserver\dao\TownDAO$1.class
com\aionemu\gameserver\model\items\ItemStone.class
com\aionemu\gameserver\network\aion\clientpackets\CM_GROUP_LOOT.class
com\aionemu\gameserver\controllers\RVController$2.class
com\aionemu\gameserver\skillengine\effect\SupportEventEffect.class
com\aionemu\gameserver\network\aion\serverpackets\AbstractPlayerInfoPacket.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CREATE_CHARACTER.class
com\aionemu\gameserver\dataholders\HouseNpcsData.class
com\aionemu\gameserver\skillengine\effect\CarveSignetEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SUMMON_UPDATE.class
com\aionemu\gameserver\skillengine\effect\SummonSkillAreaEffect$1.class
com\aionemu\gameserver\model\templates\npcshout\NpcShout.class
com\aionemu\gameserver\services\DatabaseCleaningService.class
com\aionemu\gameserver\model\siege\OutpostLocation.class
com\aionemu\gameserver\services\instance\PeriodicInstanceManager.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MEGAPHONE.class
com\aionemu\gameserver\model\autogroup\EntryRequestType.class
com\aionemu\gameserver\model\templates\pet\PetFlavour.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ABYSS_RANKING_PLAYERS.class
com\aionemu\gameserver\dao\HeadhuntingDAO$1.class
com\aionemu\gameserver\model\templates\recipe\RecipeTemplate.class
com\aionemu\gameserver\dao\PlayerSettingsDAO.class
com\aionemu\gameserver\model\gameobjects\player\title\TitleList.class
com\aionemu\gameserver\model\skill\PlayerSkillEntry$1.class
com\aionemu\gameserver\dao\AdventDAO.class
com\aionemu\gameserver\services\transfers\CMT_CHARACTER_INFORMATION.class
com\aionemu\gameserver\services\LegionService$SingletonHolder.class
com\aionemu\gameserver\questEngine\handlers\template\KillInZone.class
com\aionemu\gameserver\services\teleport\TeleportService$SpawnTask.class
com\aionemu\gameserver\skillengine\effect\AlwaysBlockEffect$1.class
com\aionemu\gameserver\services\drop\DropDistributionService$SingletonHolder.class
com\aionemu\gameserver\controllers\FlyController.class
com\aionemu\gameserver\model\templates\spawns\riftspawns\RiftSpawn.class
com\aionemu\gameserver\model\Race.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_UPDATE_TITLE.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\StartQuestOperation.class
com\aionemu\gameserver\controllers\CreatureController$DelayedOnAttack.class
com\aionemu\gameserver\controllers\movement\NpcMoveController$Destination.class
com\aionemu\gameserver\model\stats\container\NpcLifeStats.class
com\aionemu\gameserver\dataholders\StaticData.class
com\aionemu\gameserver\questEngine\model\QuestEnv.class
com\aionemu\gameserver\model\town\Town$1.class
com\aionemu\gameserver\services\player\SecurityTokenService.class
com\aionemu\gameserver\dataholders\NpcFactionsData.class
com\aionemu\gameserver\network\aion\iteminfo\ArrowInfoBlobEntry.class
com\aionemu\gameserver\controllers\attack\PlayerAggroList.class
com\aionemu\gameserver\GameServer.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PACKAGE_INFO_NOTIFY.class
com\aionemu\gameserver\custom\instance\CustomInstanceService$SingletonHolder.class
com\aionemu\gameserver\spawnengine\SpawnEngine.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$21.class
com\aionemu\gameserver\dataholders\SkillChargeData.class
com\aionemu\gameserver\skillengine\effect\EffectTemplate.class
com\aionemu\gameserver\skillengine\effect\WeaponStatboostEffect.class
com\aionemu\gameserver\network\aion\AionConnection$State.class
com\aionemu\gameserver\controllers\movement\NpcMoveController.class
com\aionemu\gameserver\ai\manager\WalkManager$2.class
com\aionemu\gameserver\network\aion\clientpackets\CM_REGISTER_HOUSE.class
com\aionemu\gameserver\model\gameobjects\state\CreatureState.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BROKER_SETTLE_ACCOUNT.class
com\aionemu\gameserver\network\aion\AionClientPacketFactory$PacketInfo.class
com\aionemu\gameserver\model\team\alliance\events\AssignViceCaptainEvent.class
com\aionemu\gameserver\network\aion\clientpackets\CM_AUTO_GROUP.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$13.class
com\aionemu\gameserver\services\reward\StarterKitService.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\GiveItemOperation.class
com\aionemu\gameserver\skillengine\condition\ChainCondition.class
com\aionemu\gameserver\services\worldraid\WorldRaid.class
com\aionemu\gameserver\model\templates\itemset\FullBonus.class
com\aionemu\gameserver\services\ClassChangeService$1.class
com\aionemu\gameserver\dao\AnnouncementsDAO$2.class
com\aionemu\gameserver\model\items\storage\Storage.class
com\aionemu\gameserver\model\team\legion\LegionHistoryAction.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_REGISTRY.class
com\aionemu\gameserver\model\templates\item\actions\ExpExtractAction$2.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PLACE_BID.class
com\aionemu\gameserver\controllers\movement\SiegeWeaponMoveController.class
com\aionemu\gameserver\services\AccountService.class
com\aionemu\gameserver\model\templates\item\AssembledItem.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropNpcGroups.class
com\aionemu\gameserver\geoEngine\scene\Spatial$CullHint.class
com\aionemu\gameserver\dao\PlayerRecipesDAO.class
com\aionemu\gameserver\taskmanager\tasks\TeamStatUpdater.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TRADELIST.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$18.class
com\aionemu\gameserver\model\templates\factions\NpcFactionTemplate.class
com\aionemu\gameserver\skillengine\effect\AbsoluteEXPPointHealInstantEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_DEBUG_COMMAND.class
com\aionemu\gameserver\controllers\attack\AggroInfo.class
com\aionemu\gameserver\services\HTMLService.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\QuestOperations.class
com\aionemu\gameserver\services\LegionDominionService.class
com\aionemu\gameserver\model\templates\towns\TownSpawn.class
com\aionemu\gameserver\network\aion\iteminfo\AccessoryInfoBlobEntry.class
com\aionemu\gameserver\model\templates\item\actions\ChargeAction$1.class
com\aionemu\gameserver\model\instance\InstanceBuff.class
com\aionemu\gameserver\model\templates\stats\KiskStatsTemplate.class
com\aionemu\gameserver\model\stats\calc\functions\StatShieldMasteryFunction.class
com\aionemu\gameserver\model\broker\filter\BrokerRecipeFilter.class
com\aionemu\gameserver\world\zone\ZoneService.class
com\aionemu\gameserver\services\StigmaService$3.class
com\aionemu\gameserver\model\ingameshop\InGameShopEn.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_MEMBERLIST.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DP_INFO.class
com\aionemu\gameserver\skillengine\model\PenaltySkill.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$AetherPowderBiscuit.class
com\aionemu\gameserver\model\templates\spawns\SpawnMap.class
com\aionemu\gameserver\model\templates\ai\Bombs.class
com\aionemu\gameserver\model\templates\flypath\FlyPathEntry.class
com\aionemu\gameserver\geoEngine\collision\bih\BIHTree.class
com\aionemu\gameserver\services\ExchangeService$SingletonHolder.class
com\aionemu\gameserver\services\ban\ChatBanService$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_DISCONNECT.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$26.class
com\aionemu\gameserver\ai\handler\ThinkEventHandler.class
com\aionemu\gameserver\dao\PlayerDAO$5.class
com\aionemu\gameserver\network\aion\serverpackets\SM_INSTANCE_INFO.class
com\aionemu\gameserver\model\templates\spawns\mercenaries\MercenarySpawn.class
com\aionemu\gameserver\services\BrokerService$BrokerOpSaveTask.class
com\aionemu\gameserver\model\enchants\EnchantStat.class
com\aionemu\gameserver\model\templates\item\actions\QuestStartAction.class
com\aionemu\gameserver\skillengine\effect\DashEffect.class
com\aionemu\gameserver\configs\schedule\SiegeSchedules$AgentFight.class
com\aionemu\gameserver\services\panesterra\ahserion\PanesterraFaction.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PLASTIC_SURGERY.class
com\aionemu\gameserver\network\aion\clientpackets\CM_FUSION_WEAPONS.class
com\aionemu\gameserver\questEngine\handlers\template\ReportToMany.class
com\aionemu\gameserver\ai\handler\ThinkEventHandler$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_EMOTION_LIST.class
com\aionemu\gameserver\services\VortexService$2.class
com\aionemu\gameserver\ai\HpPhases$PhaseHandler.class
com\aionemu\gameserver\skillengine\effect\ConfuseEffect$ConfuseTask.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EXCHANGE_ADD_ITEM.class
com\aionemu\gameserver\configs\main\AIConfig.class
com\aionemu\gameserver\network\aion\clientpackets\CM_GET_MAIL_ATTACHMENT.class
com\aionemu\gameserver\services\teleport\PortalService.class
com\aionemu\gameserver\network\aion\serverpackets\SM_EMOTION$1.class
com\aionemu\gameserver\services\item\ItemFactory.class
com\aionemu\gameserver\dataholders\VortexData.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\QuestVarCondition.class
com\aionemu\gameserver\model\gameobjects\player\InRoll.class
com\aionemu\gameserver\model\gameobjects\player\npcFaction\ENpcFactionQuestState.class
com\aionemu\gameserver\model\templates\npcskill\NpcSkillCondition.class
com\aionemu\gameserver\services\teleport\BindPointTeleportService$1.class
com\aionemu\gameserver\controllers\movement\PlayerMoveController.class
com\aionemu\gameserver\model\team\alliance\events\PlayerConnectedEvent.class
com\aionemu\gameserver\questEngine\handlers\HandlerResult.class
com\aionemu\gameserver\model\EmotionId.class
com\aionemu\gameserver\utils\audit\GMService$SingletonHolder.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$17.class
com\aionemu\gameserver\model\templates\item\actions\EnchantItemAction$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HOUSE_OPEN_DOOR.class
com\aionemu\gameserver\model\templates\event\Buff$TriggerCondition.class
com\aionemu\gameserver\dao\PlayerTitleListDAO$1.class
com\aionemu\gameserver\model\drop\DropModifiers.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElementType$1.class
com\aionemu\gameserver\spawnengine\SpawnEngine$1.class
com\aionemu\gameserver\model\team\group\PlayerGroup.class
com\aionemu\gameserver\network\aion\clientpackets\CM_ABYSS_RANKING_LEGIONS.class
com\aionemu\gameserver\skillengine\effect\FpAttackInstantEffect.class
com\aionemu\gameserver\taskmanager\tasks\TemporaryTradeTimeTask$SingletonHolder.class
com\aionemu\gameserver\services\NpcShoutsService$SingletonHolder.class
com\aionemu\gameserver\services\PunishmentService$PunishmentType.class
com\aionemu\gameserver\questEngine\handlers\models\WorkOrdersData.class
com\aionemu\gameserver\network\aion\iteminfo\StigmaInfoBlobEntry.class
com\aionemu\gameserver\model\templates\mail\SysMail.class
com\aionemu\gameserver\model\templates\housing\PlaceArea.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropMaps.class
com\aionemu\gameserver\dao\HousesDAO.class
com\aionemu\gameserver\model\base\Base.class
com\aionemu\gameserver\model\base\BaseBossDeathListener.class
com\aionemu\gameserver\skillengine\effect\CaseHealEffect$1.class
com\aionemu\gameserver\network\chatserver\CsClientPacket.class
com\aionemu\gameserver\model\team\legion\LegionRank.class
com\aionemu\gameserver\model\templates\recipe\Component.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$12.class
com\aionemu\gameserver\model\instance\InstanceScoreType.class
com\aionemu\gameserver\model\geometry\PolyArea.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TELEPORT_LOC.class
com\aionemu\gameserver\world\knownlist\PlayerAwareKnownList.class
com\aionemu\gameserver\dao\CraftCooldownsDAO.class
com\aionemu\gameserver\skillengine\condition\Condition.class
com\aionemu\gameserver\model\gameobjects\UseableItemObject.class
com\aionemu\gameserver\model\gameobjects\HouseObject.class
com\aionemu\gameserver\model\gameobjects\player\Equipment$1$2.class
com\aionemu\gameserver\dao\HeadhuntingDAO.class
com\aionemu\gameserver\skillengine\effect\CaseHealEffect$2.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElementType$6.class
com\aionemu\gameserver\skillengine\effect\ConvertHealEffect.class
com\aionemu\gameserver\skillengine\effect\SpinEffect.class
com\aionemu\gameserver\skillengine\model\SignetData.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SHOW_DIALOG.class
com\aionemu\gameserver\utils\chathandlers\ChatProcessor.class
com\aionemu\gameserver\dao\PlayerDAO.class
com\aionemu\gameserver\model\gameobjects\CreatureTemplate.class
com\aionemu\gameserver\skillengine\model\EffectReserved.class
com\aionemu\gameserver\model\base\PanesterraFactionCamp.class
com\aionemu\gameserver\services\HousingService.class
com\aionemu\gameserver\questEngine\handlers\models\KillSpawnedData.class
com\aionemu\gameserver\dataholders\DecomposableItemsData.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$22.class
com\aionemu\gameserver\model\templates\item\ItemUseLimits.class
com\aionemu\gameserver\model\instance\instanceposition\InstancePositionHandler.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PRIVATE_STORE_NAME.class
com\aionemu\gameserver\network\aion\clientpackets\CM_QUESTION_RESPONSE.class
com\aionemu\gameserver\services\item\ItemChargeService$1.class
com\aionemu\gameserver\services\reward\BonusService$1.class
com\aionemu\gameserver\model\templates\mail\Sender.class
com\aionemu\gameserver\services\mail\SiegeResult.class
com\aionemu\gameserver\spawnengine\SpawnEngine$StatsCollector.class
com\aionemu\gameserver\model\templates\windstreams\Location2D.class
com\aionemu\gameserver\model\team\group\events\PlayerGroupLeavedEvent.class
com\aionemu\gameserver\model\gameobjects\player\FriendList$Status.class
com\aionemu\gameserver\configs\main\WorldConfig.class
com\aionemu\gameserver\model\templates\ai\SummonGroup.class
com\aionemu\gameserver\skillengine\task\GatheringTask$1.class
com\aionemu\gameserver\utils\collections\DynamicElementCountSplitList.class
com\aionemu\gameserver\model\animations\TeleportAnimation.class
com\aionemu\gameserver\configs\administration\CommandsConfig.class
com\aionemu\gameserver\model\templates\walker\RouteParent.class
com\aionemu\gameserver\services\siege\FortressSiege$1.class
com\aionemu\gameserver\ShutdownHook.class
com\aionemu\gameserver\model\stats\calc\StatCondition.class
com\aionemu\gameserver\model\templates\itemset\ItemPart.class
com\aionemu\gameserver\network\aion\serverpackets\SM_EXCHANGE_REQUEST.class
com\aionemu\gameserver\dao\MailDAO.class
com\aionemu\gameserver\skillengine\effect\ActivateEnslaveEffect.class
com\aionemu\gameserver\utils\stats\CalculationType.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_ACCOUNT_AUTH.class
com\aionemu\gameserver\utils\ChatUtil.class
com\aionemu\gameserver\model\base\CasualBase.class
com\aionemu\gameserver\services\ExchangeService$ExchangeOpSaveTask.class
com\aionemu\gameserver\skillengine\effect\BlindEffect$1.class
com\aionemu\gameserver\skillengine\condition\CombatCheckCondition.class
com\aionemu\gameserver\questEngine\handlers\template\ItemCollecting.class
com\aionemu\gameserver\network\aion\skillinfo\SkillEntryWriter.class
com\aionemu\gameserver\utils\TimeUtil.class
com\aionemu\gameserver\ai\manager\EmoteManager.class
com\aionemu\gameserver\model\templates\item\Idian.class
com\aionemu\gameserver\controllers\FlyRingController.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BROKER_SETTLE_LIST.class
com\aionemu\gameserver\services\ExchangeService$ExchangePeriodicTaskManager.class
com\aionemu\gameserver\services\panesterra\ahserion\AhserionRaid.class
com\aionemu\gameserver\network\aion\serverpackets\SM_QUEST_ACTION.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BUILDER_COMMAND.class
com\aionemu\gameserver\services\StigmaService$2.class
com\aionemu\gameserver\network\aion\clientpackets\CM_ITEM_REMODEL.class
com\aionemu\gameserver\controllers\observer\AbstractCollisionObserver.class
com\aionemu\gameserver\model\templates\walker\WalkerTemplate.class
com\aionemu\gameserver\model\TribeClass.class
com\aionemu\gameserver\services\VortexService$1.class
com\aionemu\gameserver\skillengine\task\AbstractCraftTask.class
com\aionemu\gameserver\services\item\ItemSocketService$1.class
com\aionemu\gameserver\skillengine\effect\ShapeChangeEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SHOW_NPC_ON_MAP.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\QuestStatusCondition$1.class
com\aionemu\gameserver\model\templates\world\WeatherTable.class
com\aionemu\gameserver\model\templates\item\actions\DecorateAction.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EQUIP_ITEM.class
com\aionemu\gameserver\ai\handler\MoveEventHandler.class
com\aionemu\gameserver\model\gameobjects\player\Player$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HOUSE_SETTINGS.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RECIPE_COOLDOWN.class
com\aionemu\gameserver\skillengine\properties\TargetSpeciesProperty.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EXCHANGE_REQUEST$1.class
com\aionemu\gameserver\model\templates\item\MultiReturnItem.class
com\aionemu\gameserver\services\LegionService.class
com\aionemu\gameserver\model\templates\tradelist\TradeListTemplate$TradeTab.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PING.class
com\aionemu\gameserver\model\gameobjects\TransformModel.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob.class
com\aionemu\gameserver\configs\main\RatesConfig.class
com\aionemu\gameserver\custom\instance\RoahCustomInstanceHandler.class
com\aionemu\gameserver\model\autogroup\AGPlayer.class
com\aionemu\gameserver\model\stats\calc\functions\MaxMpFunction.class
com\aionemu\gameserver\spawnengine\WorldWalkerFormations.class
com\aionemu\gameserver\model\templates\npc\TalkInfo.class
com\aionemu\gameserver\network\aion\serverpackets\SM_STATS_STATUS_UNK.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EMOTION$1.class
com\aionemu\gameserver\model\templates\Bounds.class
com\aionemu\gameserver\model\templates\item\ItemQuality.class
com\aionemu\gameserver\services\ArmsfusionService$1.class
com\aionemu\gameserver\services\panesterra\ahserion\PanesterraTeam.class
com\aionemu\gameserver\model\templates\gather\GatherableTemplate.class
com\aionemu\gameserver\taskmanager\tasks\ExpireTimerTask.class
com\aionemu\gameserver\services\craft\CraftService$2.class
com\aionemu\gameserver\model\templates\item\actions\MegaphoneAction.class
com\aionemu\gameserver\services\PvpService$SingletonHolder.class
com\aionemu\gameserver\dao\PlayerRecipesDAO$1.class
com\aionemu\gameserver\configs\main\SecurityConfig.class
com\aionemu\gameserver\skillengine\effect\DeformEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_USE_OBJECT.class
com\aionemu\gameserver\network\aion\serverpackets\SM_KEY.class
com\aionemu\gameserver\model\team\alliance\events\ChangeMemberGroupEvent.class
com\aionemu\gameserver\model\stats\calc\functions\PhysicalAccuracyFunction.class
com\aionemu\gameserver\dao\PlayerDAO$6.class
com\aionemu\gameserver\model\gameobjects\findGroup\GroupApplication.class
com\aionemu\gameserver\model\gameobjects\Pet.class
com\aionemu\gameserver\skillengine\model\SubEffectType.class
com\aionemu\gameserver\model\gameobjects\player\RecipeList.class
com\aionemu\gameserver\ai\manager\WalkManager$3.class
com\aionemu\gameserver\dao\ChallengeTasksDAO.class
com\aionemu\gameserver\model\geometry\Point3D.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MANTRA_EFFECT.class
com\aionemu\gameserver\model\team\group\events\GroupDisbandEvent.class
com\aionemu\gameserver\model\templates\itemgroups\BonusItemGroup.class
com\aionemu\gameserver\dao\LegionMemberDAO.class
com\aionemu\gameserver\model\craft\ExpertQuestsList.class
com\aionemu\gameserver\services\abyss\AbyssSkillService.class
com\aionemu\gameserver\services\KiskService.class
com\aionemu\gameserver\model\stats\calc\functions\StatRateFunction.class
com\aionemu\gameserver\network\aion\iteminfo\EnchantInfoBlobEntry.class
com\aionemu\gameserver\model\instance\instancescore\DarkPoetaScore.class
com\aionemu\gameserver\model\summons\SkillOrder.class
com\aionemu\gameserver\model\templates\worldraid\WorldRaidNpc.class
com\aionemu\gameserver\skillengine\properties\TargetRelationProperty.class
com\aionemu\gameserver\network\aion\clientpackets\CM_REJECT_REVIVE.class
com\aionemu\gameserver\model\gameobjects\siege\SiegeNpc.class
com\aionemu\gameserver\services\item\ItemRestrictionService.class
com\aionemu\gameserver\model\gameobjects\StaticObject.class
com\aionemu\gameserver\model\templates\zone\Cylinder.class
com\aionemu\gameserver\model\gameobjects\Homing.class
com\aionemu\gameserver\model\templates\housing\LimitType.class
com\aionemu\gameserver\model\templates\spawns\vortexspawns\VortexSpawnTemplate.class
com\aionemu\gameserver\ai\NpcAI$1.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$2.class
com\aionemu\gameserver\skillengine\effect\ShieldMasteryEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MANASTONE.class
com\aionemu\gameserver\dataholders\PortalLocData.class
com\aionemu\gameserver\skillengine\effect\PetrificationEffect.class
com\aionemu\gameserver\services\MixfightService$SingletonHolder.class
com\aionemu\gameserver\model\team\alliance\PlayerAlliance.class
com\aionemu\gameserver\skillengine\effect\ReturnPointEffect.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$PoppySnackTasty.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FORCED_MOVE.class
com\aionemu\gameserver\skillengine\effect\LimitedReduceDamageEffect.class
com\aionemu\gameserver\controllers\observer\TerrainZoneCollisionMaterialActor.class
com\aionemu\gameserver\geoEngine\scene\mesh\IndexIntBuffer.class
com\aionemu\gameserver\custom\instance\CustomInstanceService.class
com\aionemu\gameserver\model\templates\item\actions\SkillUseAction.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$StinkingJunk.class
com\aionemu\gameserver\skillengine\periodicaction\HpUsePeriodicAction.class
com\aionemu\gameserver\model\items\ItemMask.class
com\aionemu\gameserver\questEngine\handlers\models\MonsterHuntData.class
com\aionemu\gameserver\utils\captcha\DDSConverter$Color.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$3.class
com\aionemu\gameserver\network\aion\serverpackets\SM_QUEST_LIST.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_HISTORY.class
com\aionemu\gameserver\model\templates\quest\QuestMentorType.class
com\aionemu\gameserver\model\templates\road\RoadExit.class
com\aionemu\gameserver\model\templates\assemblednpc\AssembledNpcTemplate$AssembledNpcPartTemplate.class
com\aionemu\gameserver\model\templates\item\actions\InstanceTimeClear$2.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PLAYER_STATUS_INFO$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_REPORT_PLAYER.class
com\aionemu\gameserver\model\gameobjects\player\Macros$Macro.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_UPDATE_EMBLEM.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MEGAPHONE.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PLAYER_INFO.class
com\aionemu\gameserver\model\animations\ArrivalAnimation.class
com\aionemu\gameserver\dataholders\SpawnsData$1.class
com\aionemu\gameserver\dataholders\PetDopingData.class
com\aionemu\gameserver\dataholders\PlayerInitialData$PlayerCreationData.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropNpc.class
com\aionemu\gameserver\skillengine\properties\TargetRelationProperty$1.class
com\aionemu\gameserver\dataholders\NpcData.class
com\aionemu\gameserver\model\team\league\events\LeagueInviteEvent.class
com\aionemu\gameserver\utils\stats\AbyssRankEnum.class
com\aionemu\gameserver\model\templates\event\AtreianPassport.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$8.class
com\aionemu\gameserver\services\QuestService$2.class
com\aionemu\gameserver\geoEngine\scene\mesh\IndexByteBuffer.class
com\aionemu\gameserver\controllers\PlaceableObjectController.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SUMMON_OWNER_REMOVE.class
com\aionemu\gameserver\model\templates\mail\Title.class
com\aionemu\gameserver\model\templates\npcskill\NpcSkillSpawn.class
com\aionemu\gameserver\skillengine\effect\DispelBuffCounterAtkEffect.class
com\aionemu\gameserver\skillengine\effect\Effects.class
com\aionemu\gameserver\model\templates\materials\MaterialActCondition.class
com\aionemu\gameserver\dao\PlayerDAO$1.class
com\aionemu\gameserver\services\vortex\Invasion$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_DISTRIBUTION_SETTINGS.class
com\aionemu\gameserver\skillengine\effect\SubTypeBoostResistEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_REGISTER_BROKER_ITEM.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PET$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_OBJECT_USE_UPDATE.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalRule.class
com\aionemu\gameserver\instance\handlers\GeneralInstanceHandler.class
com\aionemu\gameserver\services\SkillLearnService.class
com\aionemu\gameserver\skillengine\effect\ConfuseEffect.class
com\aionemu\gameserver\dao\LegionDominionDAO.class
com\aionemu\gameserver\geoEngine\utils\TempVars$TempVarsStack.class
com\aionemu\gameserver\skillengine\effect\SwitchHostileEffect.class
com\aionemu\gameserver\skillengine\properties\TargetSpeciesProperty$1.class
com\aionemu\gameserver\dao\CommandsAccessDAO.class
com\aionemu\gameserver\model\gameobjects\Item.class
com\aionemu\gameserver\services\RoadService$SingletonHolder.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_ACOUNT_AUTH_RESPONSE.class
com\aionemu\gameserver\model\templates\staticdoor\StaticDoorState.class
com\aionemu\gameserver\model\templates\zone\Sphere.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$7.class
com\aionemu\gameserver\model\team\group\events\PlayerGroupLeavedEvent$1.class
com\aionemu\gameserver\model\vortex\VortexLocation.class
com\aionemu\gameserver\utils\chathandlers\AdminCommand.class
com\aionemu\gameserver\model\Chance.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RESTORE_CHARACTER.class
com\aionemu\gameserver\geoEngine\collision\CollisionIntention.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CLIENT_COMMAND_ROLL.class
com\aionemu\gameserver\model\templates\housing\Parts.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHARACTER_EDIT.class
com\aionemu\gameserver\model\templates\npc\NpcTemplateType.class
com\aionemu\gameserver\dataholders\HouseData.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$9.class
com\aionemu\gameserver\skillengine\effect\ProtectEffect$1.class
com\aionemu\gameserver\model\stats\container\StatEnum.class
com\aionemu\gameserver\services\panesterra\ahserion\AhserionRaid$1.class
com\aionemu\gameserver\model\stats\calc\functions\PvEAttackRatioFunction.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType.class
com\aionemu\gameserver\skillengine\model\SkillSubType.class
com\aionemu\gameserver\geoEngine\collision\WorldBoundCollisionResults.class
com\aionemu\gameserver\model\templates\item\GodstoneInfo.class
com\aionemu\gameserver\skillengine\model\SkillTargetSlot$1.class
com\aionemu\gameserver\model\templates\item\actions\InstanceTimeClear$1.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_MACBAN_LIST.class
com\aionemu\gameserver\model\gameobjects\player\AbyssRank$AbyssRankUpdateType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_UPDATE_NOTE.class
com\aionemu\gameserver\skillengine\change\Func.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEGION_DOMINION_REQUEST_RANKING.class
com\aionemu\gameserver\skillengine\effect\ProtectEffect$2.class
com\aionemu\gameserver\configs\main\HTMLConfig.class
com\aionemu\gameserver\model\templates\item\purification\ItemPurificationTemplate.class
com\aionemu\gameserver\network\aion\clientpackets\CM_DUEL_REQUEST.class
com\aionemu\gameserver\model\team\league\events\LeagueMoveEvent.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ABYSS_ARTIFACT_INFO3.class
com\aionemu\gameserver\services\drop\DropRegistrationService$1.class
com\aionemu\gameserver\model\challenge\ChallengeTask.class
com\aionemu\gameserver\services\craft\CraftService$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_UNK_3_5_1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CREATE_CHARACTER.class
com\aionemu\gameserver\skillengine\effect\HostileUpEffect.class
com\aionemu\gameserver\model\gameobjects\player\Rates$13.class
com\aionemu\gameserver\model\templates\item\actions\SkillLearnAction.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HOUSE_PAY_RENT.class
com\aionemu\gameserver\network\Crypt.class
com\aionemu\gameserver\dataholders\PetData.class
com\aionemu\gameserver\model\base\BaseOccupier.class
com\aionemu\gameserver\configs\main\InstanceConfig.class
com\aionemu\gameserver\world\exceptions\AlreadySpawnedException.class
com\aionemu\gameserver\skillengine\effect\MagicCounterAtkEffect.class
com\aionemu\gameserver\skillengine\effect\SubTypeExtendDurationEffect.class
com\aionemu\gameserver\model\templates\shield\ShieldPoint.class
com\aionemu\gameserver\skillengine\properties\Properties$ValidationResult.class
com\aionemu\gameserver\controllers\PetController.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_BAN_RESPONSE.class
com\aionemu\gameserver\model\instance\InstanceBuff$InstanceBuffTask.class
com\aionemu\gameserver\model\templates\towns\TownLevel.class
com\aionemu\gameserver\services\BrokerService$1.class
com\aionemu\gameserver\events\AbstractEvent.class
com\aionemu\gameserver\ai\NpcAI.class
com\aionemu\gameserver\model\templates\item\actions\RideAction$4.class
com\aionemu\gameserver\model\gameobjects\findGroup\GroupRecruitment.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_CHANGE_ALLOWED_HDD_SERIAL.class
com\aionemu\gameserver\model\templates\item\actions\DecomposeAction$2.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_ACCOUNT_DISCONNECTED.class
com\aionemu\gameserver\model\templates\item\actions\SummonHouseObjectAction.class
com\aionemu\gameserver\model\house\PlayerScript.class
com\aionemu\gameserver\model\instance\instancescore\HarmonyArenaScore.class
com\aionemu\gameserver\model\ingameshop\InGameShop.class
com\aionemu\gameserver\taskmanager\tasks\PlayerMoveTaskManager.class
com\aionemu\gameserver\world\WorldMap3DInstance.class
com\aionemu\gameserver\dataholders\AbsoluteStatsData.class
com\aionemu\gameserver\services\reward\WebRewardService.class
com\aionemu\gameserver\questEngine\handlers\template\MentorMonsterHunt.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_DOMINION_LOC_INFO.class
com\aionemu\gameserver\world\zone\handler\ZoneHandler.class
com\aionemu\gameserver\model\stats\container\ServantGameStats.class
com\aionemu\gameserver\network\aion\clientpackets\CM_QUEST_SHARE.class
com\aionemu\gameserver\skillengine\effect\OpenAerialEffect.class
com\aionemu\gameserver\ai\handler\SpawnEventHandler.class
com\aionemu\gameserver\skillengine\effect\AlwaysBlockEffect.class
com\aionemu\gameserver\model\gameobjects\player\HouseOwnerState.class
com\aionemu\gameserver\model\summons\SummonMode.class
com\aionemu\gameserver\model\templates\mail\StringParamList.class
com\aionemu\gameserver\services\abyss\AbyssService.class
com\aionemu\gameserver\model\stats\calc\functions\PhysicalAttackFunction.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$AetherGemBiscuit.class
com\aionemu\gameserver\custom\instance\CustomInstanceRank.class
com\aionemu\gameserver\model\siege\AssaulterType.class
com\aionemu\gameserver\controllers\movement\MovementMask.class
com\aionemu\gameserver\dataholders\InstanceCooltimeData$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_INSTANCE_STAGE_INFO.class
com\aionemu\gameserver\model\templates\item\actions\RideAction.class
com\aionemu\gameserver\dao\LegionDominionDAO$1.class
com\aionemu\gameserver\skillengine\effect\InvulnerableWingEffect.class
com\aionemu\gameserver\network\aion\AionClientPacketFactory.class
com\aionemu\gameserver\skillengine\effect\ProvokerEffect$1.class
com\aionemu\gameserver\network\chatserver\CsServerPacket.class
com\aionemu\gameserver\services\TradeService.class
com\aionemu\gameserver\services\item\ItemPacketService$ItemDeleteType.class
com\aionemu\gameserver\services\RespawnService$RespawnTask.class
com\aionemu\gameserver\model\house\House$1.class
com\aionemu\gameserver\skillengine\effect\ReflectorEffect.class
com\aionemu\gameserver\model\templates\gather\ExMaterials.class
com\aionemu\gameserver\dao\BrokerDAO$3.class
com\aionemu\gameserver\skillengine\effect\OneTimeBoostSkillAttackEffect.class
com\aionemu\gameserver\services\RechargerService.class
com\aionemu\gameserver\skillengine\model\EffectReserved$ResourceType.class
com\aionemu\gameserver\skillengine\condition\ItemChargeCondition.class
com\aionemu\gameserver\services\event\EventBuffHandler.class
com\aionemu\gameserver\model\templates\zone\AreaType.class
com\aionemu\gameserver\controllers\observer\ShieldObserver.class
com\aionemu\gameserver\skillengine\effect\DelayedSkillEffect.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$4.class
com\aionemu\gameserver\skillengine\effect\ChangeHateOnAttackedEffect$1.class
com\aionemu\gameserver\dao\FriendListDAO$3.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElementType$8.class
com\aionemu\gameserver\model\templates\item\bonuses\RandomBonusSet.class
com\aionemu\gameserver\model\templates\siegelocation\ArtifactActivation.class
com\aionemu\gameserver\utils\collections\FixedElementCountSplitList.class
com\aionemu\gameserver\world\zone\handler\MaterialZoneHandler.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEGION_UPLOAD_EMBLEM.class
com\aionemu\gameserver\model\templates\housing\Sale.class
com\aionemu\gameserver\model\templates\zone\ZoneTemplate.class
com\aionemu\gameserver\services\siege\FortressAssault$1.class
com\aionemu\gameserver\model\ChatType.class
com\aionemu\gameserver\skillengine\effect\RootEffect.class
com\aionemu\gameserver\dao\LegionMemberDAO$5.class
com\aionemu\gameserver\network\aion\serverpackets\SM_BIND_POINT_TELEPORT.class
com\aionemu\gameserver\skillengine\effect\FlyoffEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_WEATHER.class
com\aionemu\gameserver\services\reward\WebRewardService$MaxLevelReward.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SUMMON_PANEL.class
com\aionemu\gameserver\model\gameobjects\PictureObject.class
com\aionemu\gameserver\model\templates\panels\SkillPanel.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$FeedBoneGroup.class
com\aionemu\gameserver\services\CubeExpandService.class
com\aionemu\gameserver\network\aion\clientpackets\CM_VERSION_CHECK.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$32.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SECURITY_TOKEN.class
com\aionemu\gameserver\model\actions\PlayerMode.class
com\aionemu\gameserver\ai\handler\AttackEventHandler.class
com\aionemu\gameserver\model\gameobjects\player\AbsoluteStatOwner.class
com\aionemu\gameserver\model\templates\spawns\SpawnGroup.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GROUP_LOOT.class
com\aionemu\gameserver\utils\annotations\AnnotatedMethod.class
com\aionemu\gameserver\model\templates\rewards\CraftReward.class
com\aionemu\gameserver\skillengine\effect\AlwaysHitEffect.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropItem.class
com\aionemu\gameserver\model\stats\container\TrapGameStats.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ABYSS_RANK.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$11.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_BAN.class
com\aionemu\gameserver\questEngine\handlers\models\NpcInfos.class
com\aionemu\gameserver\services\player\PlayerReviveService.class
com\aionemu\gameserver\controllers\observer\RoadObserver.class
com\aionemu\gameserver\questEngine\model\QuestState.class
com\aionemu\gameserver\services\item\ItemActionService$1.class
com\aionemu\gameserver\model\team\GeneralTeam.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CASTSPELL.class
com\aionemu\gameserver\taskmanager\AbstractFIFOPeriodicTaskManager.class
com\aionemu\gameserver\model\stats\calc\functions\BlockFunction.class
com\aionemu\gameserver\dao\FriendListDAO.class
com\aionemu\gameserver\dao\ItemCooldownsDAO.class
com\aionemu\gameserver\model\skill\SkillEntry.class
com\aionemu\gameserver\model\team\alliance\events\PlayerAllianceLeavedEvent.class
com\aionemu\gameserver\model\templates\pet\PetDopingEntry.class
com\aionemu\gameserver\dataholders\PlayerInitialData$LocationData.class
com\aionemu\gameserver\services\ban\HDDBanService$SingletonHolder.class
com\aionemu\gameserver\model\siege\ArtifactStatus.class
com\aionemu\gameserver\skillengine\model\Skill$2.class
com\aionemu\gameserver\services\DuelService.class
com\aionemu\gameserver\taskmanager\tasks\housing\MaintenanceTask.class
com\aionemu\gameserver\skillengine\effect\modifier\TargetRaceDamageModifier.class
com\aionemu\gameserver\services\siege\FortressAssault.class
com\aionemu\gameserver\skillengine\effect\SummonBindingGroupGateEffect.class
com\aionemu\gameserver\ai\handler\FreezeEventHandler.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\PcInventoryCondition$1.class
com\aionemu\gameserver\controllers\observer\ItemUseObserver.class
com\aionemu\gameserver\dao\PlayerDAO$7.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SKILL_ACTIVATION.class
com\aionemu\gameserver\services\BrokerService.class
com\aionemu\gameserver\network\loginserver\LoginServer$LoginRequest.class
com\aionemu\gameserver\model\team\legion\LegionEmblemType.class
com\aionemu\gameserver\model\templates\teleport\TeleporterTemplate.class
com\aionemu\gameserver\controllers\observer\AttackShieldObserver.class
com\aionemu\gameserver\taskmanager\tasks\TeamMoveUpdater$SingletonHolder.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MACRO_RESULT.class
com\aionemu\gameserver\model\templates\housing\HousingStorage.class
com\aionemu\gameserver\model\templates\shield\ShieldTemplate.class
com\aionemu\gameserver\model\gameobjects\player\npcFaction\NpcFaction.class
com\aionemu\gameserver\services\drop\DropRegistrationService.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PLAYER_SEARCH.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$23.class
com\aionemu\gameserver\model\broker\filter\BrokerFilter.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropZone.class
com\aionemu\gameserver\model\templates\housing\BuildingType.class
com\aionemu\gameserver\model\stats\calc\NpcStatCalculation$1.class
com\aionemu\gameserver\custom\instance\CustomInstanceRankedPlayer.class
com\aionemu\gameserver\model\templates\quest\CollectItems.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GAMEGUARD.class
com\aionemu\gameserver\skillengine\effect\SkillAtkDrainInstantEffect.class
com\aionemu\gameserver\model\templates\item\actions\PolishAction$1.class
com\aionemu\gameserver\model\items\storage\LegionStorageProxy.class
com\aionemu\gameserver\model\templates\mail\Tail.class
com\aionemu\gameserver\utils\captcha\CAPTCHAUtil.class
com\aionemu\gameserver\model\curingzone\CuringObject.class
com\aionemu\gameserver\dataholders\MaterialData.class
com\aionemu\gameserver\services\siege\SiegeException.class
com\aionemu\gameserver\ai\AIActions$2.class
com\aionemu\gameserver\services\item\ItemRestrictionService$1.class
com\aionemu\gameserver\dataholders\WorldRaidData.class
com\aionemu\gameserver\model\items\PendingTuneResult.class
com\aionemu\gameserver\controllers\CreatureController.class
com\aionemu\gameserver\network\sequrity\FloodManager$LogEntry.class
com\aionemu\gameserver\model\templates\housing\UseItemAction.class
com\aionemu\gameserver\services\craft\CraftSkillUpdateService$1.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\events\OnKillEvent.class
com\aionemu\gameserver\geoEngine\scene\VertexBuffer$Format.class
com\aionemu\gameserver\model\gameobjects\player\BlockList.class
com\aionemu\gameserver\services\LifeStatsRestoreService.class
com\aionemu\gameserver\network\aion\clientpackets\CM_DELETE_ITEM.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ALLIANCE_INFO$AllianceInfo.class
com\aionemu\gameserver\world\zone\handler\GeneralZoneHandler.class
com\aionemu\gameserver\dao\TownDAO.class
com\aionemu\gameserver\questEngine\handlers\models\XmlQuestData.class
com\aionemu\gameserver\model\gameobjects\PetSpecialFunction.class
com\aionemu\gameserver\skillengine\effect\ShieldEffect.class
com\aionemu\gameserver\network\aion\iteminfo\PremiumOptionInfoBlobEntry.class
com\aionemu\gameserver\services\RoadService.class
com\aionemu\gameserver\model\templates\itemgroups\CraftRecipeGroup.class
com\aionemu\gameserver\GameServer$2.class
com\aionemu\gameserver\model\gameobjects\player\PetCommonData.class
com\aionemu\gameserver\services\StigmaService.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$5.class
com\aionemu\gameserver\model\gameobjects\Item$1.class
com\aionemu\gameserver\dataholders\HouseBuildingData.class
com\aionemu\gameserver\dao\PlayerQuestListDAO.class
com\aionemu\gameserver\model\gameobjects\Npc.class
com\aionemu\gameserver\model\autogroup\LookingForParty.class
com\aionemu\gameserver\services\drop\DropService.class
com\aionemu\gameserver\skillengine\effect\SummonHomingEffect$1.class
com\aionemu\gameserver\model\team\alliance\events\ChangeAllianceLeaderEvent.class
com\aionemu\gameserver\model\templates\npcskill\NpcSkillTemplates.class
com\aionemu\gameserver\services\CuringZoneService$SingletonHolder.class
com\aionemu\gameserver\skillengine\model\Times.class
com\aionemu\gameserver\model\team\alliance\events\PlayerAllianceLeavedEvent$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_STATUPDATE_MP.class
com\aionemu\gameserver\geoEngine\scene\Mesh$1.class
com\aionemu\gameserver\spawnengine\ClusteredNpc.class
com\aionemu\gameserver\network\aion\clientpackets\CM_RELEASE_OBJECT.class
com\aionemu\gameserver\network\aion\clientpackets\CM_TITLE_SET.class
com\aionemu\gameserver\services\PeriodicSaveService$ServerRunTimeSaveTask.class
com\aionemu\gameserver\network\sequrity\NetFlusher.class
com\aionemu\gameserver\services\reward\VeteranRewardService$SingletonHolder.class
com\aionemu\gameserver\network\aion\iteminfo\PolishInfoBlobEntry.class
com\aionemu\gameserver\network\aion\serverpackets\SM_QUIT_RESPONSE.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\QuestCondition.class
com\aionemu\gameserver\model\vortex\VortexStateType.class
com\aionemu\gameserver\controllers\RVController$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_TELEPORT_SELECT.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CHANNEL_INFO.class
com\aionemu\gameserver\network\aion\serverpackets\SM_BIND_POINT_INFO.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ATTACK_STATUS.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$FeedSoulGroup.class
com\aionemu\gameserver\model\templates\vortex\VortexTemplate.class
com\aionemu\gameserver\configs\main\ShutdownConfig.class
com\aionemu\gameserver\model\house\House.class
com\aionemu\gameserver\model\stats\calc\functions\EvasionFunction.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SUBZONE_CHANGE.class
com\aionemu\gameserver\geoEngine\scene\Box.class
com\aionemu\gameserver\model\templates\stats\PetStatsTemplate.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropNpcs.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$3.class
com\aionemu\gameserver\skillengine\condition\HpCondition.class
com\aionemu\gameserver\skillengine\model\HostileType.class
com\aionemu\gameserver\model\geometry\Area.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BONUS_TITLE.class
com\aionemu\gameserver\dao\ItemStoneListDAO.class
com\aionemu\gameserver\dataholders\GoodsListData.class
com\aionemu\gameserver\dataholders\AutoGroupData.class
com\aionemu\gameserver\skillengine\effect\AbstractHealEffect.class
com\aionemu\gameserver\services\worldraid\WorldRaid$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_KISK_UPDATE.class
com\aionemu\gameserver\model\templates\Guides\SurveyTemplate.class
com\aionemu\gameserver\dao\LegionMemberDAO$3.class
com\aionemu\gameserver\network\aion\clientpackets\CM_INSTANCE_LEAVE.class
com\aionemu\gameserver\skillengine\condition\PlayerMovedCondition.class
com\aionemu\gameserver\configs\schedule\RiftSchedule$Rift.class
com\aionemu\gameserver\services\LimitedItemTradeService.class
com\aionemu\gameserver\services\LegionService$3.class
com\aionemu\gameserver\model\items\storage\IStorage.class
com\aionemu\gameserver\model\instance\playerreward\PvpInstancePlayerReward$1.class
com\aionemu\gameserver\dataholders\ItemRandomBonusData.class
com\aionemu\gameserver\model\templates\npcshout\ShoutList.class
com\aionemu\gameserver\ai\AIEngine.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SPLIT_ITEM.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$25.class
com\aionemu\gameserver\model\templates\ai\Percentage.class
com\aionemu\gameserver\model\templates\item\actions\PackAction$1.class
com\aionemu\gameserver\model\templates\spawns\basespawns\BaseSpawn$BaseOccupierTemplate.class
com\aionemu\gameserver\services\DuelService$2.class
com\aionemu\gameserver\services\LegionService$1.class
com\aionemu\gameserver\questEngine\handlers\template\WorkOrders.class
com\aionemu\gameserver\model\templates\spawns\SpawnTemplate.class
com\aionemu\gameserver\services\item\ItemService$ItemUpdatePredicate.class
com\aionemu\gameserver\model\templates\npcshout\ShoutGroup.class
com\aionemu\gameserver\model\templates\quest\HandlerSideDrop.class
com\aionemu\gameserver\controllers\movement\CreatureMoveController.class
com\aionemu\gameserver\dataholders\PetFeedData.class
com\aionemu\gameserver\skillengine\effect\SpellAtkDrainEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FRIEND_RESPONSE.class
com\aionemu\gameserver\geoEngine\scene\DespawnableNode.class
com\aionemu\gameserver\skillengine\effect\FpAttackEffect.class
com\aionemu\gameserver\skillengine\effect\PoisonEffect.class
com\aionemu\gameserver\dataholders\ChallengeData.class
com\aionemu\gameserver\network\aion\serverpackets\SM_NPC_INFO.class
com\aionemu\gameserver\services\panesterra\ahserion\AhserionRaid$SingletonHolder.class
com\aionemu\gameserver\model\stats\calc\functions\AttackSpeedFunction.class
com\aionemu\gameserver\skillengine\effect\BoostSkillCostEffect$1.class
com\aionemu\gameserver\model\templates\walker\RouteStep.class
com\aionemu\gameserver\network\aion\serverpackets\SM_UPDATE_PLAYER_APPEARANCE.class
com\aionemu\gameserver\geoEngine\scene\DespawnableNode$DespawnableType.class
com\aionemu\gameserver\model\team\league\events\LeagueLeftEvent$LeaveReson.class
com\aionemu\gameserver\controllers\observer\AbstractMaterialSkillActor$MaterialSkillTask.class
com\aionemu\gameserver\services\AnnouncementService$SingletonHolder.class
com\aionemu\gameserver\network\chatserver\ChatServer.class
com\aionemu\gameserver\model\templates\LegionDominionInvasionRift.class
com\aionemu\gameserver\model\templates\tradelist\TradeListTemplate.class
com\aionemu\gameserver\model\templates\item\actions\AssemblyItemAction$2.class
com\aionemu\gameserver\dao\BonusPackDAO.class
com\aionemu\gameserver\geoEngine\scene\mesh\IndexShortBuffer.class
com\aionemu\gameserver\dataholders\TitleData.class
com\aionemu\gameserver\configs\main\FallDamageConfig.class
com\aionemu\gameserver\model\templates\worldraid\WorldRaidLocation.class
com\aionemu\gameserver\model\templates\ai\Summons.class
com\aionemu\gameserver\model\team\group\events\PlayerGroupEnteredEvent.class
com\aionemu\gameserver\questEngine\handlers\QuestHandlerLoader.class
com\aionemu\gameserver\services\PeriodicSaveService$PeriodicSaveTask.class
com\aionemu\gameserver\skillengine\effect\FallEffect.class
com\aionemu\gameserver\skillengine\model\TransformType.class
com\aionemu\gameserver\model\team\alliance\PlayerAllianceService$OfflinePlayerAllianceChecker.class
com\aionemu\gameserver\skillengine\effect\SkillAttackInstantEffect.class
com\aionemu\gameserver\model\templates\goods\GoodsList.class
com\aionemu\gameserver\services\toypet\PetHungryLevel.class
com\aionemu\gameserver\services\siege\BalaurAssaultService.class
com\aionemu\gameserver\network\chatserver\CsClientPacketFactory.class
com\aionemu\gameserver\model\templates\itemgroups\BossGroup.class
com\aionemu\gameserver\utils\collections\SplitList.class
com\aionemu\gameserver\network\aion\clientpackets\CM_REMOVE_ALTERED_STATE.class
com\aionemu\gameserver\ai\handler\ReturningEventHandler.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ABNORMAL_STATE.class
com\aionemu\gameserver\model\stats\calc\functions\PlayerStatFunctions.class
com\aionemu\gameserver\model\siege\ArtifactLocation.class
com\aionemu\gameserver\network\aion\serverpackets\SM_QUESTION_WINDOW.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PLAYER_LISTENER.class
com\aionemu\gameserver\network\aion\serverpackets\SM_WINDSTREAM_ANNOUNCE.class
com\aionemu\gameserver\skillengine\task\AbstractInteractionTask.class
com\aionemu\gameserver\services\abyss\AbyssRankingCache.class
com\aionemu\gameserver\skillengine\change\Change.class
com\aionemu\gameserver\skillengine\effect\PolymorphEffect.class
com\aionemu\gameserver\configs\main\HousingConfig.class
com\aionemu\gameserver\model\team\league\events\LeagueCreateEvent.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CRAFT_ANIMATION.class
com\aionemu\gameserver\controllers\effect\EffectController.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_DOMINION_RANK.class
com\aionemu\gameserver\configs\main\LoggingConfig.class
com\aionemu\gameserver\services\reward\WebRewardService$1.class
com\aionemu\gameserver\model\actions\PlayerActions$1.class
com\aionemu\gameserver\services\mail\MailFormatter$2.class
com\aionemu\gameserver\services\ChallengeTaskService.class
com\aionemu\gameserver\network\aion\instanceinfo\HarmonyScoreWriter$1.class
com\aionemu\gameserver\model\gameobjects\player\QuestStateList.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MOVE_IN_AIR.class
com\aionemu\gameserver\ai\AIEngine$DummyAI.class
com\aionemu\gameserver\skillengine\properties\FirstTargetProperty$1.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$14.class
com\aionemu\gameserver\network\aion\iteminfo\CompositeItemBlobEntry.class
com\aionemu\gameserver\dao\HeadhuntingDAO$3.class
com\aionemu\gameserver\model\actions\PlayerActions.class
com\aionemu\gameserver\skillengine\effect\BoostSpellAttackEffect.class
com\aionemu\gameserver\questEngine\handlers\template\AbstractTemplateQuestHandler.class
com\aionemu\gameserver\controllers\TrapController.class
com\aionemu\gameserver\network\chatserver\clientpackets\CM_CS_PLAYER_AUTH_RESPONSE.class
com\aionemu\gameserver\model\templates\item\actions\CompositionAction$1.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$16.class
com\aionemu\gameserver\skillengine\effect\SanctuaryEffect.class
com\aionemu\gameserver\events\AbstractEventSource.class
com\aionemu\gameserver\model\templates\staticdoor\StaticDoorTemplate.class
com\aionemu\gameserver\model\house\HouseBids$Bid.class
com\aionemu\gameserver\model\templates\quest\QuestNpc.class
com\aionemu\gameserver\model\stats\calc\functions\PhysicalCriticalFunction.class
com\aionemu\gameserver\network\aion\serverpackets\SM_WINDSTREAM.class
com\aionemu\gameserver\network\aion\serverpackets\SM_UI_SETTINGS.class
com\aionemu\gameserver\model\templates\item\actions\AbstractItemAction.class
com\aionemu\gameserver\model\templates\item\actions\RideAction$2.class
com\aionemu\gameserver\world\knownlist\KnownList.class
com\aionemu\gameserver\services\rift\RiftInformer.class
com\aionemu\gameserver\questEngine\handlers\models\CraftingRewardsData.class
com\aionemu\gameserver\model\templates\rift\RiftTemplate.class
com\aionemu\gameserver\skillengine\effect\MPHealInstantEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_TARGET_SELECT.class
com\aionemu\gameserver\questEngine\handlers\template\FountainRewards.class
com\aionemu\gameserver\controllers\observer\FlyRingObserver.class
com\aionemu\gameserver\dataholders\FlyPathData.class
com\aionemu\gameserver\dao\RewardServiceDAO.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\events\QuestEvent.class
com\aionemu\gameserver\model\stats\container\SummonedObjectGameStats$1.class
com\aionemu\gameserver\model\challenge\ChallengeQuest.class
com\aionemu\gameserver\model\templates\portal\PortalDialog.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ACTION_ANIMATION.class
com\aionemu\gameserver\model\instance\instanceposition\GeneralInstancePosition.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropWorlds.class
com\aionemu\gameserver\taskmanager\tasks\TemporaryTradeTimeTask.class
com\aionemu\gameserver\model\items\ItemSlot.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHANGE_CHANNEL.class
com\aionemu\gameserver\model\templates\quest\QuestItems.class
com\aionemu\gameserver\ai\AbstractAI.class
com\aionemu\gameserver\model\house\HouseDoorState.class
com\aionemu\gameserver\model\geometry\Polygon2D.class
com\aionemu\gameserver\model\templates\windstreams\WindstreamPath.class
com\aionemu\gameserver\configs\main\PeriodicSaveConfig.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ATTACK_STATUS$LOG.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GROUP_INFO.class
com\aionemu\gameserver\geoEngine\scene\VertexBuffer$Type.class
com\aionemu\gameserver\dao\PlayerRegisteredItemsDAO.class
com\aionemu\gameserver\ai\handler\AggroEventHandler.class
com\aionemu\gameserver\model\templates\itemgroups\OreGroup.class
com\aionemu\gameserver\network\chatserver\serverpackets\SM_CS_PLAYER_LOGOUT.class
com\aionemu\gameserver\ai\AIState.class
com\aionemu\gameserver\services\CronJobService$IdianDepthPortalSpawner.class
com\aionemu\gameserver\skillengine\effect\WeaponStatupEffect.class
com\aionemu\gameserver\model\templates\mail\IMailFormatter.class
com\aionemu\gameserver\model\templates\towns\TownSpawnMap.class
com\aionemu\gameserver\skillengine\effect\HostileUpEffect$2.class
com\aionemu\gameserver\network\chatserver\CsClientPacketFactory$PacketInfo.class
com\aionemu\gameserver\model\templates\pet\PetRewards.class
com\aionemu\gameserver\services\craft\CraftSkillUpdateService.class
com\aionemu\gameserver\model\templates\item\actions\TitleAddAction.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CASTSPELL_RESULT.class
com\aionemu\gameserver\geoEngine\bounding\BoundingVolume$Type.class
com\aionemu\gameserver\skillengine\effect\BufEffect$1.class
com\aionemu\gameserver\dataholders\MapWeatherData.class
com\aionemu\gameserver\model\gameobjects\player\ReviveType.class
com\aionemu\gameserver\utils\idfactory\IDFactoryError.class
com\aionemu\gameserver\model\templates\item\actions\StigmaUnlockAction.class
com\aionemu\gameserver\model\town\Town.class
com\aionemu\gameserver\model\DialogAction.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$12.class
com\aionemu\gameserver\world\WorldMap2DInstance.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_UPDATE_SELF_INTRO.class
com\aionemu\gameserver\model\templates\event\EventQuestList.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$7.class
com\aionemu\gameserver\network\aion\serverpackets\SM_EXCHANGE_ADD_KINAH.class
com\aionemu\gameserver\services\ArmsfusionService.class
com\aionemu\gameserver\skillengine\effect\StatdownEffect.class
com\aionemu\gameserver\model\instance\instanceposition\GloryInstancePosition.class
com\aionemu\gameserver\model\templates\item\ItemTemplate.class
com\aionemu\gameserver\dataholders\loadingutils\adapters\NpcEquippedGearAdapter.class
com\aionemu\gameserver\model\siege\FortressLocation$SiegeBuffAction.class
com\aionemu\gameserver\model\team\common\events\PlayerEnteredEvent.class
com\aionemu\gameserver\skillengine\condition\FormCondition.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PLAYER_STANCE.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEGION_UPLOAD_INFO.class
com\aionemu\gameserver\model\stats\calc\functions\StatFunction.class
com\aionemu\gameserver\skillengine\effect\APBoostEffect.class
com\aionemu\gameserver\skillengine\effect\AbstractHealEffect$1.class
com\aionemu\gameserver\model\team\common\events\PlayerLeavedEvent$LeaveReson.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$FeedArmor.class
com\aionemu\gameserver\controllers\HouseController.class
com\aionemu\gameserver\skillengine\effect\WeaponMasteryEffect.class
com\aionemu\gameserver\skillengine\effect\SimpleRootEffect.class
com\aionemu\gameserver\model\instance\instanceposition\DisciplineInstancePosition.class
com\aionemu\gameserver\services\craft\RelinquishCraftStatus.class
com\aionemu\gameserver\skillengine\effect\BuffBindEffect.class
com\aionemu\gameserver\services\DevilsMarkService$SingletonHolder.class
com\aionemu\gameserver\model\team\common\service\PlayerTeamCommandService.class
com\aionemu\gameserver\geoEngine\scene\Geometry.class
com\aionemu\gameserver\model\templates\item\actions\DyeAction.class
com\aionemu\gameserver\model\team\common\service\PlayerTeamCommandService$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_BROKER_SERVICE$BrokerPacketType.class
com\aionemu\gameserver\model\templates\item\actions\InstanceTimeClear.class
com\aionemu\gameserver\dataholders\InstanceBuffData.class
com\aionemu\gameserver\network\aion\iteminfo\ItemBlobEntry.class
com\aionemu\gameserver\skillengine\condition\RaceCondition.class
com\aionemu\gameserver\dao\LegionDAO$3.class
com\aionemu\gameserver\questEngine\handlers\models\Monster.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LOOT_ITEMLIST.class
com\aionemu\gameserver\skillengine\effect\FPHealEffect.class
com\aionemu\gameserver\model\gameobjects\player\Rates$8.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$FeedFluid.class
com\aionemu\gameserver\services\SiegeService.class
com\aionemu\gameserver\dao\LegionDominionDAO$3.class
com\aionemu\gameserver\ai\event\AIListenable.class
com\aionemu\gameserver\model\templates\siegelocation\SiegeMercenaryZone.class
com\aionemu\gameserver\model\templates\item\DecomposableItemInfo.class
com\aionemu\gameserver\services\toypet\PetAdoptionService.class
com\aionemu\gameserver\skillengine\effect\AlwaysResistEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_GATHER.class
com\aionemu\gameserver\services\instance\PvPArenaService.class
com\aionemu\gameserver\model\templates\item\actions\ExtractAction.class
com\aionemu\gameserver\dao\HouseScriptsDAO.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GM_SHOW_PLAYER_STATUS.class
com\aionemu\gameserver\network\aion\instanceinfo\ArenaScoreWriter.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SKILL_REMOVE.class
com\aionemu\gameserver\controllers\attack\AddDamageEvent.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SKILL_COOLDOWN.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ALLIANCE_MEMBER_INFO$1.class
com\aionemu\gameserver\services\abyss\AbyssSkills.class
com\aionemu\gameserver\dao\BrokerDAO$5.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SIEGE_LOCATION_STATE.class
com\aionemu\gameserver\skillengine\condition\DpCondition.class
com\aionemu\gameserver\dao\PlayerMacrosDAO$3.class
com\aionemu\gameserver\model\templates\L10n.class
com\aionemu\gameserver\geoEngine\math\Matrix4f.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PRICES.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CRAFT.class
com\aionemu\gameserver\model\gameobjects\BrokerItem.class
com\aionemu\gameserver\model\gameobjects\MoveableObject.class
com\aionemu\gameserver\model\gameobjects\player\PlayerAppearance.class
com\aionemu\gameserver\model\templates\npc\GroupDropType.class
com\aionemu\gameserver\network\aion\clientpackets\CM_GF_WEBSHOP_TOKEN_REQUEST.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\DialogIdCondition$1.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$HealthyFoodAll.class
com\aionemu\gameserver\services\AutoGroupService$NewSingletonHolder.class
com\aionemu\gameserver\model\templates\housing\HousingCategory.class
com\aionemu\gameserver\dataholders\loadingutils\XmlDataLoader.class
com\aionemu\gameserver\dao\PlayerDAO$11.class
com\aionemu\gameserver\model\gameobjects\PetAction.class
com\aionemu\gameserver\dao\PlayerCooldownsDAO.class
com\aionemu\gameserver\model\templates\housing\HousingJukeBox.class
com\aionemu\gameserver\dao\FriendListDAO$1.class
com\aionemu\gameserver\model\templates\item\actions\ChargeAction.class
com\aionemu\gameserver\services\panesterra\PanesterraService$SingletonHolder.class
com\aionemu\gameserver\skillengine\effect\DRBoostEffect.class
com\aionemu\gameserver\model\instance\instanceposition\ChaosInstancePosition.class
com\aionemu\gameserver\model\templates\item\actions\EmotionLearnAction.class
com\aionemu\gameserver\model\templates\zone\WorldZoneTemplate.class
com\aionemu\gameserver\model\templates\quest\XMLStartCondition.class
com\aionemu\gameserver\model\gameobjects\player\Rates$4.class
com\aionemu\gameserver\world\zone\ZoneUpdateService$SingletonHolder.class
com\aionemu\gameserver\network\aion\instanceinfo\InstanceScoreWriter.class
com\aionemu\gameserver\configs\administration\AdminConfig.class
com\aionemu\gameserver\model\gameobjects\player\Rates.class
com\aionemu\gameserver\dao\BlockListDAO$1.class
com\aionemu\gameserver\dao\InGameShopDAO.class
com\aionemu\gameserver\model\templates\housing\HousingLand.class
com\aionemu\gameserver\model\templates\staticdoor\StaticDoorWorld.class
com\aionemu\gameserver\world\World$SingletonHolder.class
com\aionemu\gameserver\skillengine\model\HitType.class
com\aionemu\gameserver\model\stats\container\PlayerLifeStats.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PONG.class
com\aionemu\gameserver\model\templates\item\actions\ApExtractAction.class
com\aionemu\gameserver\model\instance\playerreward\PvpInstancePlayerReward.class
com\aionemu\gameserver\model\templates\item\actions\AnimationAddAction.class
com\aionemu\gameserver\model\templates\quest\CollectItem.class
com\aionemu\gameserver\questEngine\handlers\models\KillInWorldData.class
com\aionemu\gameserver\questEngine\model\QuestStatus.class
com\aionemu\gameserver\model\items\ItemStone$ItemStoneType.class
com\aionemu\gameserver\dao\PlayerBindPointDAO$1.class
com\aionemu\gameserver\model\templates\spawns\panesterra\AhserionsFlightSpawnTemplate.class
com\aionemu\gameserver\network\sequrity\FloodManager.class
com\aionemu\gameserver\utils\xml\StringSchemaOutputResolver.class
com\aionemu\gameserver\dao\MailDAO$7.class
com\aionemu\gameserver\model\instance\instancescore\NormalScore.class
com\aionemu\gameserver\model\templates\item\actions\EnchantItemAction.class
com\aionemu\gameserver\network\aion\clientpackets\CM_USE_CHARGE_SKILL.class
com\aionemu\gameserver\skillengine\effect\SummonFunctionalNpcEffect.class
com\aionemu\gameserver\model\templates\rewards\FullRewardItem.class
com\aionemu\gameserver\model\animations\AttackTypeAnimation.class
com\aionemu\gameserver\model\templates\itemgroups\EnchantGroup.class
com\aionemu\gameserver\network\aion\serverpackets\SM_POSITION.class
com\aionemu\gameserver\controllers\StaticObjectController.class
com\aionemu\gameserver\configs\main\MembershipConfig.class
com\aionemu\gameserver\dataholders\ItemData.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\ActionItemUseOperation$1.class
com\aionemu\gameserver\skillengine\effect\BuffSilenceEffect.class
com\aionemu\gameserver\model\templates\itemgroups\EventGroup.class
com\aionemu\gameserver\dao\MailDAO$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ENTER_WORLD_CHECK.class
com\aionemu\gameserver\services\LegionService$5.class
com\aionemu\gameserver\services\conquerorAndProtectorSystem\CPInfo.class
com\aionemu\gameserver\services\SiegeService$1.class
com\aionemu\gameserver\dao\LegionDAO$7.class
com\aionemu\gameserver\model\stats\calc\functions\StatAbsFunction.class
com\aionemu\gameserver\model\team\legion\LegionMemberEx.class
com\aionemu\gameserver\skillengine\effect\ProtectEffect.class
com\aionemu\gameserver\model\stats\calc\functions\PvEDefendRatioFunction.class
com\aionemu\gameserver\services\ban\HDDBanService.class
com\aionemu\gameserver\model\team\common\service\PlayerTeamDistributionService.class
com\aionemu\gameserver\network\chatserver\serverpackets\SM_CS_AUTH.class
com\aionemu\gameserver\network\aion\clientpackets\CM_RESTORE_CHARACTER.class
com\aionemu\gameserver\model\EmotionType.class
com\aionemu\gameserver\model\templates\spawns\siegespawns\SiegeSpawn.class
com\aionemu\gameserver\geoEngine\math\FastMath.class
com\aionemu\gameserver\questEngine\handlers\template\KillSpawned.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\ActionItemUseOperation.class
com\aionemu\gameserver\configs\main\PlayerTransferConfig.class
com\aionemu\gameserver\model\PlayerClass.class
com\aionemu\gameserver\skillengine\effect\SummonGroupGateEffect$1.class
com\aionemu\gameserver\model\templates\ai\AITemplate.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElementType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FIRST_SHOW_DECOMPOSABLE.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_TELEPORT.class
com\aionemu\gameserver\dataholders\WalkerVersionsData.class
com\aionemu\gameserver\dataholders\GuideHtmlData.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MOVE.class
com\aionemu\gameserver\geoEngine\bounding\BoundingBox.class
com\aionemu\gameserver\dataholders\PetSkillData.class
com\aionemu\gameserver\dao\SurveyControllerDAO.class
com\aionemu\gameserver\services\vortex\GeneratorDestroyListener.class
com\aionemu\gameserver\services\siege\ArtifactAssault.class
com\aionemu\gameserver\dao\AbyssRankDAO$RankingListPlayerGp.class
com\aionemu\gameserver\model\templates\item\Disposition.class
com\aionemu\gameserver\world\container\PlayerContainer.class
com\aionemu\gameserver\services\mail\MailService.class
com\aionemu\gameserver\dataholders\TeleporterData.class
com\aionemu\gameserver\taskmanager\tasks\PlayerMoveTaskManager$SingletonHolder.class
com\aionemu\gameserver\model\team\alliance\events\CheckAllianceReadyEvent.class
com\aionemu\gameserver\network\aion\AionServerPacket.class
com\aionemu\gameserver\world\WorldDropType.class
com\aionemu\gameserver\services\WeatherService$1.class
com\aionemu\gameserver\model\team\group\events\ChangeGroupLootRulesEvent.class
com\aionemu\gameserver\services\RespawnService$DecayTask.class
com\aionemu\gameserver\model\templates\housing\AbstractHouseObject.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ALLIANCE_INFO.class
com\aionemu\gameserver\model\templates\pet\PetDopingBag.class
com\aionemu\gameserver\utils\audit\AutoBan.class
com\aionemu\gameserver\model\templates\chest\ChestTemplate.class
com\aionemu\gameserver\dao\PlayerEffectsDAO$2.class
com\aionemu\gameserver\model\team\group\PlayerGroupMember.class
com\aionemu\gameserver\model\templates\item\ResultedItemsCollection.class
com\aionemu\gameserver\model\gameobjects\NpcObject.class
com\aionemu\gameserver\skillengine\task\GatheringTask.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEGION_SEND_EMBLEM_INFO.class
com\aionemu\gameserver\services\TownService$1.class
com\aionemu\gameserver\model\templates\item\purification\RequiredMaterial.class
com\aionemu\gameserver\network\sequrity\NetFlusher$1.class
com\aionemu\gameserver\services\DialogService.class
com\aionemu\gameserver\skillengine\effect\DispelNpcBuffEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SUMMON_ATTACK.class
com\aionemu\gameserver\configs\schedule\SiegeSchedules$Fortress.class
com\aionemu\gameserver\services\LifeStatsRestoreService$HpMpRestoreTask.class
com\aionemu\gameserver\utils\collections\ListPart.class
com\aionemu\gameserver\model\templates\quest\InventoryItem.class
com\aionemu\gameserver\dao\MailDAO$5.class
com\aionemu\gameserver\model\team\common\legacy\LootGroupRules$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PET_EMOTE.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropMap.class
com\aionemu\gameserver\skillengine\condition\LeftHandCondition.class
com\aionemu\gameserver\skillengine\effect\ProcFPHealInstantEffect.class
com\aionemu\gameserver\custom\instance\neuralnetwork\DataSet.class
com\aionemu\gameserver\geoEngine\scene\GLObject.class
com\aionemu\gameserver\model\team\alliance\events\CheckAllianceReadyEvent$1.class
com\aionemu\gameserver\model\gameobjects\player\Rates$19.class
com\aionemu\gameserver\network\aion\clientpackets\CM_DELETE_MAIL.class
com\aionemu\gameserver\controllers\observer\ObserverType.class
com\aionemu\gameserver\model\templates\itemset\PartBonus.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropNpcName.class
com\aionemu\gameserver\services\TribeRelationService.class
com\aionemu\gameserver\skillengine\effect\StatboostEffect.class
com\aionemu\gameserver\model\templates\rewards\MedicineItem.class
com\aionemu\gameserver\skillengine\effect\UtilityEffect.class
com\aionemu\gameserver\network\chatserver\ChatServerConnection.class
com\aionemu\gameserver\skillengine\effect\SkillCooltimeResetEffect.class
com\aionemu\gameserver\dao\BlockListDAO$3.class
com\aionemu\gameserver\model\stats\calc\functions\StatAddFunction.class
com\aionemu\gameserver\model\gameobjects\player\Rates$2.class
com\aionemu\gameserver\services\HousingBidService.class
com\aionemu\gameserver\controllers\attack\KillCounter.class
com\aionemu\gameserver\skillengine\effect\MagicCounterAtkEffect$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TARGET_SELECTED.class
com\aionemu\gameserver\dataholders\HousingObjectData.class
com\aionemu\gameserver\model\gameobjects\BrokerItem$7.class
com\aionemu\gameserver\model\stats\listeners\ItemEquipmentListener.class
com\aionemu\gameserver\model\templates\zone\ZoneClassName.class
com\aionemu\gameserver\skillengine\effect\HideEffect$5.class
com\aionemu\gameserver\model\templates\expand\Expand.class
com\aionemu\gameserver\services\item\HouseObjectFactory.class
com\aionemu\gameserver\model\skill\PlayerSkillList.class
com\aionemu\gameserver\services\OneVsOneService.class
com\aionemu\gameserver\dao\PlayerPunishmentsDAO$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HOUSE_SCRIPT.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$AetherCrystalBiscuitGroup.class
com\aionemu\gameserver\network\aion\serverpackets\SM_IN_GAME_SHOP_LIST.class
com\aionemu\gameserver\model\templates\item\WeaponStats.class
com\aionemu\gameserver\skillengine\model\ActivationAttribute.class
com\aionemu\gameserver\skillengine\effect\SwitchHpMpEffect.class
com\aionemu\gameserver\skillengine\effect\SpellAtkDrainInstantEffect.class
com\aionemu\gameserver\services\conquerorAndProtectorSystem\CPBuff.class
com\aionemu\gameserver\network\aion\instanceinfo\TheShugoEmperorsVaultScoreWriter.class
com\aionemu\gameserver\dataholders\GlobalDropData.class
com\aionemu\gameserver\model\account\CharacterPasskey.class
com\aionemu\gameserver\services\ban\ChatBanService.class
com\aionemu\gameserver\model\templates\npcskill\QueuedNpcSkillTemplate.class
com\aionemu\gameserver\dao\PlayerSettingsDAO$2.class
com\aionemu\gameserver\controllers\observer\AbstractMaterialSkillActor.class
com\aionemu\gameserver\model\templates\npcskill\NpcSkillConditionTemplate.class
com\aionemu\gameserver\model\team\league\LeagueService.class
com\aionemu\gameserver\model\stats\calc\functions\PvPAttackRatioFunction.class
com\aionemu\gameserver\utils\annotations\AnnotatedClassImpl.class
com\aionemu\gameserver\model\skill\PlayerSkillEntry.class
com\aionemu\gameserver\model\instance\instancescore\LegionDominionScore.class
com\aionemu\gameserver\skillengine\effect\WeaponDualEffect.class
com\aionemu\gameserver\skillengine\properties\Properties$CastState.class
com\aionemu\gameserver\dataholders\RiftData.class
com\aionemu\gameserver\skillengine\periodicaction\PeriodicActions.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LOGIN_QUEUE.class
com\aionemu\gameserver\model\gameobjects\player\Friend.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PING_REQUEST.class
com\aionemu\gameserver\dataholders\MotionData$AnimationTimes.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$30.class
com\aionemu\gameserver\model\templates\item\actions\RemodelAction.class
com\aionemu\gameserver\dataholders\HousePartsData.class
com\aionemu\gameserver\dao\GuideDAO.class
com\aionemu\gameserver\dataholders\loadingutils\XmlMerger$ImportFileHashChecker.class
com\aionemu\gameserver\network\loginserver\LoginServerConnection$State.class
com\aionemu\gameserver\skillengine\effect\AbstractOverTimeEffect.class
com\aionemu\gameserver\skillengine\effect\SpellAttackInstantEffect.class
com\aionemu\gameserver\model\templates\materials\MeshList.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BUY_BROKER_ITEM.class
com\aionemu\gameserver\skillengine\effect\TargetTeleportEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SHOW_RESTRICTIONS.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BROKER_CANCEL_REGISTERED.class
com\aionemu\gameserver\network\aion\serverpackets\SM_AFTER_TIME_CHECK_4_7_5.class
com\aionemu\gameserver\network\aion\AionConnection$ConnectionAliveChecker.class
com\aionemu\gameserver\model\gameobjects\player\ResponseRequester.class
com\aionemu\gameserver\skillengine\effect\ResurrectEffect.class
com\aionemu\gameserver\skillengine\effect\HideEffect$3.class
com\aionemu\gameserver\services\CronJobService$1.class
com\aionemu\gameserver\network\aion\GameConnectionFactoryImpl.class
com\aionemu\gameserver\questEngine\task\checker\CoordinateDestinationChecker.class
com\aionemu\gameserver\skillengine\SkillEngine$1.class
com\aionemu\gameserver\configs\main\GroupConfig.class
com\aionemu\gameserver\dataholders\PlayerInitialData.class
com\aionemu\gameserver\geoEngine\models\Terrain.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BROKER_LIST.class
com\aionemu\gameserver\skillengine\properties\MaxCountProperty.class
com\aionemu\gameserver\model\craft\MasterQuestsList.class
com\aionemu\gameserver\skillengine\effect\PetOrderUseUltraSkillEffect.class
com\aionemu\gameserver\model\gameobjects\Creature.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_LS_PONG.class
com\aionemu\gameserver\services\teleport\TeleportService.class
com\aionemu\gameserver\dao\PlayerPunishmentsDAO$5.class
com\aionemu\gameserver\network\aion\serverpackets\AbstractHouseInfoPacket.class
com\aionemu\gameserver\model\templates\bounty\BountyTemplate.class
com\aionemu\gameserver\ai\manager\WalkManager.class
com\aionemu\gameserver\network\aion\iteminfo\EquippedSlotBlobEntry.class
com\aionemu\gameserver\configs\schedule\SiegeSchedules.class
com\aionemu\gameserver\network\aion\serverpackets\SM_STATUPDATE_DP.class
com\aionemu\gameserver\model\gameobjects\BrokerItem$9.class
com\aionemu\gameserver\model\templates\event\upgradearcade\ArcadeLevels.class
com\aionemu\gameserver\questEngine\handlers\template\SkillUse.class
com\aionemu\gameserver\skillengine\effect\AbstractDispelEffect.class
com\aionemu\gameserver\services\conquerorAndProtectorSystem\ConquerorAndProtectorService$SingletonHolder.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FRIEND_NOTIFY.class
com\aionemu\gameserver\network\aion\serverpackets\SM_IN_GAME_SHOP_ITEM.class
com\aionemu\gameserver\ai\eventcallback\OnDieEventListener.class
com\aionemu\gameserver\dao\PortalCooldownsDAO.class
com\aionemu\gameserver\services\RiftService$RiftServiceHolder.class
com\aionemu\gameserver\services\vortex\Invasion.class
com\aionemu\gameserver\spawnengine\SpawnHandlerType.class
com\aionemu\gameserver\model\siege\Influence.class
com\aionemu\gameserver\ai\handler\TargetEventHandler$1.class
com\aionemu\gameserver\model\gameobjects\player\motion\Motion.class
com\aionemu\gameserver\skillengine\properties\AreaDirections.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_PREMIUM_RESPONSE.class
com\aionemu\gameserver\dao\BrokerDAO$1.class
com\aionemu\gameserver\model\autogroup\AGQuestion.class
com\aionemu\gameserver\model\team\legion\LegionPermissionsMask.class
com\aionemu\gameserver\model\gameobjects\player\Rates$11.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SKILL_LIST.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CASTSPELL_RESULT$1.class
com\aionemu\gameserver\dao\AccountPassportsDAO.class
com\aionemu\gameserver\model\gameobjects\player\Rates$17.class
com\aionemu\gameserver\model\gameobjects\player\PlayerScripts.class
com\aionemu\gameserver\dao\LegionDAO$1.class
com\aionemu\gameserver\model\base\BaseLocation.class
com\aionemu\gameserver\model\ingameshop\InGameShopEn$DescFilter.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BREAK_WEAPONS.class
com\aionemu\gameserver\controllers\movement\SummonMoveController.class
com\aionemu\gameserver\controllers\observer\GaleCycloneObserver.class
com\aionemu\gameserver\model\account\PassportsList.class
com\aionemu\gameserver\services\DialogService$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_NPC_ASSEMBLER.class
com\aionemu\gameserver\network\aion\instanceinfo\PvpInstanceScoreWriter.class
com\aionemu\gameserver\services\siege\OutpostSiege.class
com\aionemu\gameserver\model\team\legion\LegionMember.class
com\aionemu\gameserver\events\Listenable.class
com\aionemu\gameserver\instance\InstanceEngine.class
com\aionemu\gameserver\model\gameobjects\BrokerItem$5.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RESURRECT.class
com\aionemu\gameserver\dao\VeteranRewardDAO.class
com\aionemu\gameserver\model\templates\teleport\TelelocationTemplate.class
com\aionemu\gameserver\model\gameobjects\player\Rates$15.class
com\aionemu\gameserver\controllers\attack\AttackResult.class
com\aionemu\gameserver\dao\PlayerPunishmentsDAO$3.class
com\aionemu\gameserver\model\templates\QuestTemplate$1.class
com\aionemu\gameserver\skillengine\effect\MpAttackInstantEffect.class
com\aionemu\gameserver\network\loginserver\LsClientPacket.class
com\aionemu\gameserver\services\HousingService$SingletonHolder.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GM_BOOKMARK_ADD.class
com\aionemu\gameserver\services\AutoGroupService$1.class
com\aionemu\gameserver\model\templates\materials\MeshMaterial.class
com\aionemu\gameserver\spawnengine\WalkerFormationsCache.class
com\aionemu\gameserver\model\gameobjects\BrokerItem$3.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BUY_ITEM.class
com\aionemu\gameserver\network\aion\serverpackets\SM_STATUPDATE_EXP.class
com\aionemu\gameserver\services\item\ItemSplitService$1.class
com\aionemu\gameserver\skillengine\properties\FirstTargetRangeProperty.class
com\aionemu\gameserver\skillengine\model\SignetEnum.class
com\aionemu\gameserver\model\templates\pet\PetFunction.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GATHERABLE_INFO.class
com\aionemu\gameserver\configs\schedule\SiegeSchedules$SiegeSchedule.class
com\aionemu\gameserver\geoEngine\collision\Collidable.class
com\aionemu\gameserver\model\instance\instanceposition\HarmonyInstancePosition.class
com\aionemu\gameserver\custom\pvpmap\PvpMapHandler$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PET.class
com\aionemu\gameserver\network\aion\instanceinfo\LegionDominionScoreWriter.class
com\aionemu\gameserver\skillengine\effect\AlwaysDodgeEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BROKER_SEARCH.class
com\aionemu\gameserver\ai\event\AIEventLog.class
com\aionemu\gameserver\skillengine\effect\DummyEffect.class
com\aionemu\gameserver\services\teleport\BindPointTeleportService$1$1.class
com\aionemu\gameserver\network\chatserver\serverpackets\SM_CS_PLAYER_AUTH.class
com\aionemu\gameserver\model\gameobjects\player\Player.class
com\aionemu\gameserver\model\team\TeamEvent.class
com\aionemu\gameserver\model\templates\base\BaseTemplate.class
com\aionemu\gameserver\model\gameobjects\player\title\Title.class
com\aionemu\gameserver\network\aion\serverpackets\SM_QUEST_ACTION$ActionType.class
com\aionemu\gameserver\ai\AIActions$1.class
com\aionemu\gameserver\services\transfers\CMT_CHARACTER_INFORMATION$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TRANSFORM_IN_SUMMON.class
com\aionemu\gameserver\model\items\GodStone.class
com\aionemu\gameserver\model\templates\housing\HousingNpc.class
com\aionemu\gameserver\controllers\movement\PlayableMoveController.class
com\aionemu\gameserver\ai\handler\CreatureEventHandler.class
com\aionemu\gameserver\ai\handler\TargetEventHandler.class
com\aionemu\gameserver\model\stats\container\CreatureGameStats$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_FRIEND_DEL.class
com\aionemu\gameserver\utils\chathandlers\ChatCommandsLoader.class
com\aionemu\gameserver\skillengine\condition\RideRobotCondition.class
com\aionemu\gameserver\network\aion\clientpackets\CM_INVITE_TO_GROUP.class
com\aionemu\gameserver\services\rift\RiftInformer$1.class
com\aionemu\gameserver\services\instance\InstanceService.class
com\aionemu\gameserver\skillengine\effect\NoFlyEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PLAY_MOVIE.class
com\aionemu\gameserver\dataholders\MultiReturnItemData.class
com\aionemu\gameserver\services\player\MultiClientingService.class
com\aionemu\gameserver\model\templates\survey\CustomSurveyItem.class
com\aionemu\gameserver\services\MixfightService$MixfightParticipant.class
com\aionemu\gameserver\skillengine\model\SkillCategory.class
com\aionemu\gameserver\model\templates\housing\HousePart.class
com\aionemu\gameserver\model\templates\item\actions\CosmeticItemAction.class
com\aionemu\gameserver\model\team\group\PlayerGroupService$OfflinePlayerChecker.class
com\aionemu\gameserver\model\broker\BrokerRace.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PRIVATE_STORE.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DELETE_HOUSE.class
com\aionemu\gameserver\model\templates\item\actions\MultiReturnAction$2.class
com\aionemu\gameserver\model\templates\cp\CPType.class
com\aionemu\gameserver\model\templates\portal\QuestReq.class
com\aionemu\gameserver\geoEngine\collision\UnsupportedCollisionException.class
com\aionemu\gameserver\network\aion\clientpackets\CM_DELETE_CHARACTER.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FLY_TIME.class
com\aionemu\gameserver\skillengine\effect\AbsoluteStatToPCBuffEffect.class
com\aionemu\gameserver\skillengine\model\SkillAliasPosition.class
com\aionemu\gameserver\network\aion\serverpackets\SM_WAREHOUSE_ADD_ITEM.class
com\aionemu\gameserver\model\templates\event\upgradearcade\ArcadeRewards.class
com\aionemu\gameserver\questEngine\model\ConditionUnionType.class
com\aionemu\gameserver\dataholders\CuringObjectsData.class
com\aionemu\gameserver\model\animations\ActionAnimation.class
com\aionemu\gameserver\model\team\legion\LegionHistoryEntry.class
com\aionemu\gameserver\skillengine\effect\modifier\BackDamageModifier.class
com\aionemu\gameserver\skillengine\condition\SelfFlyingCondition$1.class
com\aionemu\gameserver\model\templates\recipe\ComponentsData.class
com\aionemu\gameserver\services\RecipeService.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$PoppySnackNutritious.class
com\aionemu\gameserver\model\templates\item\actions\ExpandInventoryAction$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_UPDATE_NICKNAME.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$FeedBalaur.class
com\aionemu\gameserver\model\templates\npc\NpcRating.class
com\aionemu\gameserver\dao\PlayerDAO$3.class
com\aionemu\gameserver\model\templates\challenge\RewardType.class
com\aionemu\gameserver\services\siege\BalaurAssaultService$SingletonHolder.class
com\aionemu\gameserver\ai\handler\ActivateEventHandler.class
com\aionemu\gameserver\skillengine\effect\HideEffect$1.class
com\aionemu\gameserver\model\gameobjects\Letter.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_HDDBAN_CONTROL.class
com\aionemu\gameserver\network\aion\iteminfo\StigmaShardInfoBlobEntry.class
com\aionemu\gameserver\world\WorldMap.class
com\aionemu\gameserver\dataholders\loadingutils\XmlMerger$MergeResult.class
com\aionemu\gameserver\dao\PlayerAppearanceDAO$1.class
com\aionemu\gameserver\model\templates\housing\HousingMovieJukeBox.class
com\aionemu\gameserver\network\aion\serverpackets\SM_INVENTORY_ADD_ITEM.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$10.class
com\aionemu\gameserver\world\RegionUtil.class
com\aionemu\gameserver\network\aion\serverpackets\SM_QUEST_REPEAT.class
com\aionemu\gameserver\model\gameobjects\Summon.class
com\aionemu\gameserver\dataholders\ChestData.class
com\aionemu\gameserver\services\LifeStatsRestoreService$HpRestoreTask.class
com\aionemu\gameserver\model\stats\calc\StatOwner.class
com\aionemu\gameserver\skillengine\model\SignetDataTemplate.class
com\aionemu\gameserver\dataholders\SkillData.class
com\aionemu\gameserver\utils\xml\JAXBUtil.class
com\aionemu\gameserver\model\drop\DropItem.class
com\aionemu\gameserver\network\aion\serverpackets\SM_INVENTORY_UPDATE_ITEM$1.class
com\aionemu\gameserver\model\templates\ride\RideInfo.class
com\aionemu\gameserver\model\gameobjects\player\PortalCooldownList.class
com\aionemu\gameserver\model\stats\calc\StatCapUtil$StatLimits.class
com\aionemu\gameserver\model\templates\teleport\TeleportLocation.class
com\aionemu\gameserver\model\stats\container\HomingGameStats.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$ShugoEventCoinGroup.class
com\aionemu\gameserver\network\PacketWriteHelper.class
com\aionemu\gameserver\network\aion\instanceinfo\PvpInstanceScoreWriter$1.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropTribes.class
com\aionemu\gameserver\model\drop\NpcDrop.class
com\aionemu\gameserver\dataholders\SignetDataTemplates.class
com\aionemu\gameserver\model\templates\factions\FactionCategory.class
com\aionemu\gameserver\dao\LegionMemberDAO$4.class
com\aionemu\gameserver\network\aion\serverpackets\SM_UPGRADE_ARCADE.class
com\aionemu\gameserver\model\templates\mail\Body.class
com\aionemu\gameserver\network\aion\serverpackets\SM_INVENTORY_INFO.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RECIPE_LIST.class
com\aionemu\gameserver\skillengine\effect\HealEffectTemplate.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUBE_UPDATE$1.class
com\aionemu\gameserver\skillengine\effect\CurseEffect.class
com\aionemu\gameserver\model\templates\event\EventTemplate.class
com\aionemu\gameserver\configs\main\AutoGroupConfig.class
com\aionemu\gameserver\model\templates\item\bonuses\StatBonusType.class
com\aionemu\gameserver\dao\PlayerRecipesDAO$3.class
com\aionemu\gameserver\model\templates\item\RandomItem.class
com\aionemu\gameserver\model\templates\npcskill\ConjunctionType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_EXCHANGE_CONFIRMATION.class
com\aionemu\gameserver\controllers\observer\CollisionDieActor.class
com\aionemu\gameserver\model\team\group\events\PlayerDisconnectedEvent.class
com\aionemu\gameserver\spawnengine\TemporarySpawnEngine.class
com\aionemu\gameserver\model\templates\road\RoadTemplate.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$24.class
com\aionemu\gameserver\model\templates\cp\CPRank.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$PoppySnackNutritiousGroup.class
com\aionemu\gameserver\dataholders\InstanceExitData.class
com\aionemu\gameserver\skillengine\condition\PolishChargeCondition.class
com\aionemu\gameserver\model\stats\calc\functions\ParryFunction.class
com\aionemu\gameserver\utils\audit\AuditLogger.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_OBJECT.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CONQUEROR_PROTECTOR.class
com\aionemu\gameserver\services\siege\SiegeBossDoAddDamageListener.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PET_EMOTE$1.class
com\aionemu\gameserver\skillengine\effect\SilenceEffect.class
com\aionemu\gameserver\configs\main\PricesConfig.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEGION_HISTORY.class
com\aionemu\gameserver\questEngine\handlers\models\MentorMonsterHuntData.class
com\aionemu\gameserver\model\Expirable.class
com\aionemu\gameserver\controllers\SummonController.class
com\aionemu\gameserver\model\gameobjects\findGroup\FindGroupEntry.class
com\aionemu\gameserver\utils\annotations\AnnotatedMethodImpl.class
com\aionemu\gameserver\dao\PlayerLifeStatsDAO.class
com\aionemu\gameserver\model\templates\vortex\ResurrectionPoint.class
com\aionemu\gameserver\dao\PlayerDAO$8.class
com\aionemu\gameserver\services\transfers\PlayerTransfer.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TRANSFORM.class
com\aionemu\gameserver\model\templates\restriction\ItemCleanupTemplate.class
com\aionemu\gameserver\skillengine\effect\BlindEffect.class
com\aionemu\gameserver\dao\PlayerSettingsDAO$4.class
com\aionemu\gameserver\utils\idfactory\IDFactory.class
com\aionemu\gameserver\model\templates\siegelocation\AssaultData.class
com\aionemu\gameserver\model\gameobjects\player\PrivateStore.class
com\aionemu\gameserver\geoEngine\utils\TempVars.class
com\aionemu\gameserver\services\conquerorAndProtectorSystem\ConquerorAndProtectorService.class
com\aionemu\gameserver\world\zone\RegionZone.class
com\aionemu\gameserver\model\templates\quest\QuestTarget.class
com\aionemu\gameserver\services\transfers\TransferablePlayer.class
com\aionemu\gameserver\services\transfers\PlayerTransferService.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BLOCK_SET_REASON.class
com\aionemu\gameserver\skillengine\effect\DPHealEffect.class
com\aionemu\gameserver\model\instance\playerreward\HarmonyGroupReward.class
com\aionemu\gameserver\controllers\observer\AttackCalcObserver.class
com\aionemu\gameserver\services\siege\MercenaryLocation.class
com\aionemu\gameserver\network\aion\clientpackets\CM_OBJECT_SEARCH.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SECURITY_TOKEN.class
com\aionemu\gameserver\skillengine\condition\ChargeWeaponCondition.class
com\aionemu\gameserver\model\templates\item\actions\TuningAction.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropRating.class
com\aionemu\gameserver\model\templates\item\actions\PolishAction.class
com\aionemu\gameserver\model\templates\pet\PetStatsTemplate.class
com\aionemu\gameserver\model\gameobjects\Item$2.class
com\aionemu\gameserver\model\SkillElement.class
com\aionemu\gameserver\skillengine\model\Skill$1.class
com\aionemu\gameserver\dao\PlayerSkillListDAO.class
com\aionemu\gameserver\services\worldraid\WorldRaidRunnable.class
com\aionemu\gameserver\skillengine\SkillEngine.class
com\aionemu\gameserver\model\stats\container\SummonGameStats$1.class
com\aionemu\gameserver\model\autogroup\AutoInstanceHandler.class
com\aionemu\gameserver\utils\time\ServerTime.class
com\aionemu\gameserver\model\base\SiegeBaseLocation.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BLOCK_ADD.class
com\aionemu\gameserver\controllers\RoadController.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_LEAVE_MEMBER.class
com\aionemu\gameserver\skillengine\effect\ProcMPHealInstantEffect.class
com\aionemu\gameserver\model\templates\bounty\KillBountyTemplate.class
com\aionemu\gameserver\model\stats\listeners\TitleChangeListener.class
com\aionemu\gameserver\services\MixfightService.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$FeedFluidGroup.class
com\aionemu\gameserver\services\findgroup\FindGroupService$SingletonHolder.class
com\aionemu\gameserver\controllers\observer\ZoneCollisionMaterialActor.class
com\aionemu\gameserver\services\LegionService$LegionRestrictions.class
com\aionemu\gameserver\world\geo\GeoService.class
com\aionemu\gameserver\skillengine\effect\PetOrderUnSummonEffect.class
com\aionemu\gameserver\model\gameobjects\UseableItemObject$UseDataWriter.class
com\aionemu\gameserver\skillengine\effect\DelayedFpAtkInstantEffect.class
com\aionemu\gameserver\utils\stats\XPLossEnum.class
com\aionemu\gameserver\model\onevsone\OneVsOneMatch$MatchState.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$15.class
com\aionemu\gameserver\model\siege\SiegeRace$1.class
com\aionemu\gameserver\model\stats\calc\AdditionStat.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ATTACK_RESPONSE.class
com\aionemu\gameserver\dao\AbyssRankDAO.class
com\aionemu\gameserver\model\enchants\EnchantEffect.class
com\aionemu\gameserver\skillengine\effect\MpAttackEffect.class
com\aionemu\gameserver\model\templates\quest\QuestRepeatCycle.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElementType$4.class
com\aionemu\gameserver\model\templates\item\actions\ExpExtractAction.class
com\aionemu\gameserver\model\geometry\Polyline2D.class
com\aionemu\gameserver\model\stats\calc\StatCapUtil.class
com\aionemu\gameserver\model\items\storage\IStorage$1.class
com\aionemu\gameserver\controllers\observer\AttackerCriticalStatus.class
com\aionemu\gameserver\controllers\SiegeWeaponController.class
com\aionemu\gameserver\model\templates\itemgroups\FeedItemGroup.class
com\aionemu\gameserver\model\team\common\legacy\PlayerAllianceEvent.class
com\aionemu\gameserver\model\templates\event\InventoryDrop.class
com\aionemu\gameserver\ai\follow\FollowSummonTaskAI.class
com\aionemu\gameserver\model\skill\NpcSkillEntry.class
com\aionemu\gameserver\model\templates\item\actions\Level65BoostAction$2.class
com\aionemu\gameserver\model\templates\road\RoadPoint.class
com\aionemu\gameserver\dao\ServerVariablesDAO.class
com\aionemu\gameserver\services\WeatherService$SingletonHolder.class
com\aionemu\gameserver\dao\ItemCooldownsDAO$1.class
com\aionemu\gameserver\taskmanager\tasks\housing\AuctionEndTask.class
com\aionemu\gameserver\model\gameobjects\player\emotion\EmotionList.class
com\aionemu\gameserver\model\team\alliance\PlayerAllianceGroup.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\NpcIdCondition$1.class
com\aionemu\gameserver\model\templates\item\TradeinItem.class
com\aionemu\gameserver\taskmanager\tasks\housing\AuctionEndTask$ProlongedAuction.class
com\aionemu\gameserver\model\broker\BrokerPlayerCache.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TUNE_RESULT.class
com\aionemu\gameserver\services\FlyRingService$SingletonHolder.class
com\aionemu\gameserver\skillengine\effect\modifier\ActionModifiers.class
com\aionemu\gameserver\model\templates\rewards\BonusType.class
com\aionemu\gameserver\dao\LegionDominionDAO$2.class
com\aionemu\gameserver\model\templates\item\enums\EquipType.class
com\aionemu\gameserver\model\team\TemporaryPlayerTeam.class
com\aionemu\gameserver\model\stats\container\SummonedObjectGameStats.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PING_RESPONSE.class
com\aionemu\gameserver\services\siege\SiegeRaceCounter.class
com\aionemu\gameserver\model\team\league\LeagueMember.class
com\aionemu\gameserver\controllers\PetController$PetUpdateTask.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MESSAGE.class
com\aionemu\gameserver\skillengine\model\SkillType.class
com\aionemu\gameserver\model\limiteditems\LimitedItem.class
com\aionemu\gameserver\skillengine\model\Effect$3.class
com\aionemu\gameserver\skillengine\model\ChainSkill.class
com\aionemu\gameserver\dataholders\AssembledNpcsData.class
com\aionemu\gameserver\utils\stats\StatFunctions.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_PAY_RENT.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ACCOUNT_PROPERTIES.class
com\aionemu\gameserver\dao\AbyssRankDAO$1.class
com\aionemu\gameserver\model\items\storage\PlayerStorage.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\QuestConditions.class
com\aionemu\gameserver\questEngine\handlers\models\RelicRewardsData.class
com\aionemu\gameserver\model\gameobjects\state\CreatureVisualState.class
com\aionemu\gameserver\services\player\MultiClientingService$AccountSession.class
com\aionemu\gameserver\skillengine\effect\StumbleEffect.class
com\aionemu\gameserver\skillengine\model\SkillTemplate.class
com\aionemu\gameserver\model\team\league\League.class
com\aionemu\gameserver\model\account\AccountTime.class
com\aionemu\gameserver\model\templates\item\ExtractedItemsCollection.class
com\aionemu\gameserver\services\AnnouncementService.class
com\aionemu\gameserver\model\templates\item\actions\RideAction$5.class
com\aionemu\gameserver\network\aion\clientpackets\CM_UNWRAP_ITEM.class
com\aionemu\gameserver\model\templates\tribe\Tribe.class
com\aionemu\gameserver\model\team\common\events\AlwaysTrueTeamEvent.class
com\aionemu\gameserver\services\StaticDoorService.class
com\aionemu\gameserver\skillengine\model\Skill.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SET_NOTE.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$13.class
com\aionemu\gameserver\skillengine\model\DispelType.class
com\aionemu\gameserver\skillengine\effect\CondSkillLauncherEffect$1.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SHIELD_EFFECT.class
com\aionemu\gameserver\questEngine\handlers\models\KillInZoneData.class
com\aionemu\gameserver\model\EventTheme.class
com\aionemu\gameserver\model\templates\Guides\GuideTemplate.class
com\aionemu\gameserver\skillengine\effect\AuraEffect$AuraTask.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GM_SHOW_LEGION_INFO.class
com\aionemu\gameserver\world\WorldMapInstance.class
com\aionemu\gameserver\network\aion\clientpackets\CM_ABYSS_RANKING_PLAYERS.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_BIDS.class
com\aionemu\gameserver\services\item\ItemMoveService.class
com\aionemu\gameserver\services\item\ItemSocketService.class
com\aionemu\gameserver\model\templates\item\actions\DecomposeAction$1.class
com\aionemu\gameserver\model\templates\portal\ItemReq.class
com\aionemu\gameserver\services\summons\SummonsService$ReleaseSummonTask.class
com\aionemu\gameserver\network\aion\clientpackets\CM_TUNE.class
com\aionemu\gameserver\model\gameobjects\Persistable$PersistentState.class
com\aionemu\gameserver\network\aion\clientpackets\CM_GROUP_DATA_EXCHANGE.class
com\aionemu\gameserver\model\templates\portal\PortalPath.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MOVE_ITEM.class
com\aionemu\gameserver\dao\MotionDAO.class
com\aionemu\gameserver\network\aion\clientpackets\CM_USE_ITEM.class
com\aionemu\gameserver\ai\follow\FollowStartService.class
com\aionemu\gameserver\model\templates\cosmeticitems\CosmeticItemTemplate$Preset.class
com\aionemu\gameserver\services\ExchangeService.class
com\aionemu\gameserver\skillengine\model\ShieldType.class
com\aionemu\gameserver\skillengine\effect\XPBoostEffect.class
com\aionemu\gameserver\skillengine\model\Effect$ForceType.class
com\aionemu\gameserver\ai\AISubState.class
com\aionemu\gameserver\model\account\Passport$RewardStatus.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$FeedThorn.class
com\aionemu\gameserver\controllers\RVController.class
com\aionemu\gameserver\dao\AbyssRankDAO$RankingListLegion.class
com\aionemu\gameserver\ShutdownHook$SingletonHolder.class
com\aionemu\gameserver\configs\schedule\WorldRaidSchedules$WorldRaidSchedule.class
com\aionemu\gameserver\network\aion\clientpackets\CM_FRIEND_SET_MEMO.class
com\aionemu\gameserver\model\geometry\SemisphereArea.class
com\aionemu\gameserver\skillengine\effect\EffectTemplate$1.class
com\aionemu\gameserver\model\templates\rewards\RewardItem.class
com\aionemu\gameserver\model\templates\stats\AbsoluteStatsTemplate.class
com\aionemu\gameserver\services\craft\CraftService.class
com\aionemu\gameserver\skillengine\model\WeaponTypeWrapper.class
com\aionemu\gameserver\services\mail\MailFormatter$1.class
com\aionemu\gameserver\skillengine\effect\ArmorMasteryEffect.class
com\aionemu\gameserver\dataholders\ZoneData$1.class
com\aionemu\gameserver\model\instance\playerreward\InstancePlayerReward.class
com\aionemu\gameserver\services\item\ItemSplitService.class
com\aionemu\gameserver\configs\main\LegionConfig.class
com\aionemu\gameserver\world\geo\GeoService$1.class
com\aionemu\gameserver\skillengine\condition\TargetCondition.class
com\aionemu\gameserver\world\knownlist\CreatureAwareKnownList.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\SetQuestVarOperation.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TITLE_INFO.class
com\aionemu\gameserver\skillengine\effect\SearchEffect.class
com\aionemu\gameserver\model\gameobjects\player\npcFaction\NpcFactions.class
com\aionemu\gameserver\model\templates\world\WorldMapTemplate.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHECK_MAIL_UNK.class
com\aionemu\gameserver\questEngine\handlers\template\ReportTo.class
com\aionemu\gameserver\model\stats\container\PlayerGameStats.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BIND_POINT_TELEPORT.class
com\aionemu\gameserver\skillengine\effect\modifier\FrontDamageModifier.class
com\aionemu\gameserver\utils\idfactory\IDFactory$SingletonHolder.class
com\aionemu\gameserver\network\aion\serverpackets\SM_INSTANCE_COUNT_INFO.class
com\aionemu\gameserver\geoEngine\utils\BufferUtils.class
com\aionemu\gameserver\network\aion\clientpackets\CM_POSITION_SELF.class
com\aionemu\gameserver\configs\network\PffConfig.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEAVE_GROUP_MEMBER.class
com\aionemu\gameserver\dataholders\RoadData.class
com\aionemu\gameserver\network\aion\serverpackets\SM_VERSION_CHECK.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_LS_PING.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$29.class
com\aionemu\gameserver\geoEngine\math\Vector3f.class
com\aionemu\gameserver\skillengine\effect\TransformEffect.class
com\aionemu\gameserver\model\DialogPage.class
com\aionemu\gameserver\model\templates\item\TradeinList.class
com\aionemu\gameserver\network\aion\iteminfo\BonusInfoBlobEntry.class
com\aionemu\gameserver\model\assemblednpc\AssembledNpc.class
com\aionemu\gameserver\model\templates\event\Buff$Trigger.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$HealthyFoodSpicy.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CHALLENGE_LIST.class
com\aionemu\gameserver\skillengine\model\ChainSkills.class
com\aionemu\gameserver\network\aion\iteminfo\WingInfoBlobEntry.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CHAT_WINDOW.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EXCHANGE_OK.class
com\aionemu\gameserver\model\templates\event\upgradearcade\ArcadeLevel.class
com\aionemu\gameserver\services\event\EventService.class
com\aionemu\gameserver\utils\collections\CollectionUtil.class
com\aionemu\gameserver\geoEngine\scene\VertexBuffer$Usage.class
com\aionemu\gameserver\model\autogroup\AutoInstance.class
com\aionemu\gameserver\skillengine\effect\DispelEffect.class
com\aionemu\gameserver\model\templates\housing\PartType.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SUMMON_CASTSPELL.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_ACCOUNT_RECONNECT_KEY.class
com\aionemu\gameserver\ai\AI.class
com\aionemu\gameserver\model\TaskId.class
com\aionemu\gameserver\network\aion\clientpackets\CM_ITEM_PURIFICATION.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DIALOG_WINDOW.class
com\aionemu\gameserver\network\aion\clientpackets\AbstractCharacterEditPacket.class
com\aionemu\gameserver\dao\AnnouncementsDAO.class
com\aionemu\gameserver\model\templates\itemgroups\FoodGroup.class
com\aionemu\gameserver\model\instance\instancescore\InstanceScore.class
com\aionemu\gameserver\model\enchants\EnchantmentStone.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_OWNER_INFO.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_PTRANSFER_CONTROL.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHECK_MAIL_LIST.class
com\aionemu\gameserver\services\craft\CraftSkillUpdateService$SingletonHolder.class
com\aionemu\gameserver\questEngine\handlers\template\KillInWorld.class
com\aionemu\gameserver\model\templates\item\actions\StorageType.class
com\aionemu\gameserver\skillengine\effect\SummonEffect.class
com\aionemu\gameserver\model\gameobjects\player\FriendList.class
com\aionemu\gameserver\cache\HTMLCache.class
com\aionemu\gameserver\model\stats\calc\functions\StatWeaponMasteryFunction.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$18.class
com\aionemu\gameserver\network\aion\clientpackets\CM_GODSTONE_SOCKET.class
com\aionemu\gameserver\model\trade\TradeItem.class
com\aionemu\gameserver\dataholders\PlayerInitialData$PlayerCreationData$ItemType.class
com\aionemu\gameserver\model\templates\cosmeticitems\CosmeticItemTemplate.class
com\aionemu\gameserver\model\templates\housing\HousingUseableItem.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$PoppySnackGroup.class
com\aionemu\gameserver\skillengine\model\ChargeSkill.class
com\aionemu\gameserver\utils\time\gametime\DayTime.class
com\aionemu\gameserver\model\broker\filter\BrokerAllAcceptFilter.class
com\aionemu\gameserver\model\gameobjects\player\Cooldowns.class
com\aionemu\gameserver\network\aion\iteminfo\WeaponInfoBlobEntry.class
com\aionemu\gameserver\services\PeriodicSaveService$LegionWarehouseSaveTask.class
com\aionemu\gameserver\network\aion\clientpackets\CM_USE_HOUSE_OBJECT.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SYSTEM_MESSAGE.class
com\aionemu\gameserver\model\templates\BoundRadius.class
com\aionemu\gameserver\network\aion\clientpackets\CM_L2AUTH_LOGIN_CHECK.class
com\aionemu\gameserver\network\aion\serverpackets\SM_INVENTORY_UPDATE_ITEM.class
com\aionemu\gameserver\model\templates\housing\HousingPicture.class
com\aionemu\gameserver\model\items\storage\ItemStorage.class
com\aionemu\gameserver\services\player\PlayerChatService.class
com\aionemu\gameserver\model\instance\InstanceProgressionType.class
com\aionemu\gameserver\skillengine\effect\SleepEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHAT_MESSAGE_PUBLIC.class
com\aionemu\gameserver\model\templates\gather\Material.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HOUSE_DECORATE.class
com\aionemu\gameserver\model\siege\SiegeModType.class
com\aionemu\gameserver\model\templates\itemgroups\ItemRaceEntry.class
com\aionemu\gameserver\model\templates\quest\QuestCategory.class
com\aionemu\gameserver\world\zone\handler\AdvancedZoneHandler.class
com\aionemu\gameserver\model\account\Passport.class
com\aionemu\gameserver\services\UpgradeArcadeService.class
com\aionemu\gameserver\skillengine\model\SkillLearnTemplate.class
com\aionemu\gameserver\network\aion\serverpackets\SM_QUESTIONNAIRE.class
com\aionemu\gameserver\dataholders\NpcSkillData.class
com\aionemu\gameserver\skillengine\effect\CloseAerialEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MACRO_LIST.class
com\aionemu\gameserver\services\AtreianPassportService$1.class
com\aionemu\gameserver\skillengine\effect\EffectType.class
com\aionemu\gameserver\questEngine\handlers\models\FountainRewardsData.class
com\aionemu\gameserver\dataholders\BindPointData.class
com\aionemu\gameserver\world\zone\ZoneService$SingletonHolder.class
com\aionemu\gameserver\model\drop\DropGroup.class
com\aionemu\gameserver\ai\manager\FollowManager.class
com\aionemu\gameserver\network\aion\GameConnectionFactoryImpl$1.class
com\aionemu\gameserver\services\RespawnService.class
com\aionemu\gameserver\model\templates\spawns\HouseSpawn.class
com\aionemu\gameserver\utils\collections\Predicates$Players.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EXCHANGE_REQUEST.class
com\aionemu\gameserver\model\templates\flyring\FlyRingPoint.class
com\aionemu\gameserver\services\drop\DropService$SingletonHolder.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_SETTINGS.class
com\aionemu\gameserver\network\aion\clientpackets\CM_APPEARANCE.class
com\aionemu\gameserver\dataholders\ItemGroupsData$1.class
com\aionemu\gameserver\skillengine\condition\TargetFlyingCondition.class
com\aionemu\gameserver\skillengine\effect\AlwaysParryEffect$1.class
com\aionemu\gameserver\skillengine\action\DpUseAction.class
com\aionemu\gameserver\skillengine\condition\NoFlyingCondition.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\SetQuestStatusOperation.class
com\aionemu\gameserver\skillengine\model\ChargeSkillEntry.class
com\aionemu\gameserver\skillengine\action\MpUseAction.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$6.class
com\aionemu\gameserver\dao\ItemCooldownsDAO$2.class
com\aionemu\gameserver\model\templates\mail\MailMessage.class
com\aionemu\gameserver\model\templates\rift\OpenRift.class
com\aionemu\gameserver\controllers\attack\AttackStatus.class
com\aionemu\gameserver\model\templates\item\actions\ExpandInventoryAction.class
com\aionemu\gameserver\questEngine\task\checker\ZoneChecker.class
com\aionemu\gameserver\model\instance\StageList.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$FeedThornGroup.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SHOW_BLOCKLIST.class
com\aionemu\gameserver\dataholders\EnchantData.class
com\aionemu\gameserver\model\templates\challenge\ChallengeType.class
com\aionemu\gameserver\skillengine\effect\OneTimeBoostSkillAttackEffect$2.class
com\aionemu\gameserver\configs\main\InGameShopConfig.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropRace.class
com\aionemu\gameserver\model\templates\item\ItemAttackType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FRIEND_STATUS.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PLAYER_STATE.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ITEM_COOLDOWN.class
com\aionemu\gameserver\events\EventListener.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$1.class
com\aionemu\gameserver\model\stats\container\HomingGameStats$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SUMMON_COMMAND.class
com\aionemu\gameserver\model\team\common\legacy\LootGroupRules.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LOOKATOBJECT.class
com\aionemu\gameserver\skillengine\effect\BoostHateEffect.class
com\aionemu\gameserver\network\loginserver\LoginServerConnection.class
com\aionemu\gameserver\controllers\observer\AbstractQuestZoneObserver.class
com\aionemu\gameserver\dataholders\AssemblyItemsData.class
com\aionemu\gameserver\dataholders\GatherableData.class
com\aionemu\gameserver\model\templates\spawns\basespawns\BaseSpawnTemplate.class
com\aionemu\gameserver\skillengine\model\AttackType.class
com\aionemu\gameserver\services\item\ItemPacketService.class
com\aionemu\gameserver\model\templates\ingameshop\IGCategory.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_ACQUIRE.class
com\aionemu\gameserver\model\templates\zone\MaterialZoneTemplate.class
com\aionemu\gameserver\taskmanager\tasks\MovementNotifyTask$SingletonHolder.class
com\aionemu\gameserver\model\team\TeamType.class
com\aionemu\gameserver\world\knownlist\NpcKnownList.class
com\aionemu\gameserver\services\autogroup\AutoGroupUtility.class
com\aionemu\gameserver\ai\manager\SimpleAttackManager$SimpleCheckedAttackAction.class
com\aionemu\gameserver\dataholders\InstanceCooltimeData.class
com\aionemu\gameserver\skillengine\effect\ChangeHateOnAttackedEffect.class
com\aionemu\gameserver\questEngine\handlers\models\ReportToData.class
com\aionemu\gameserver\skillengine\condition\MpCondition.class
com\aionemu\gameserver\utils\captcha\DDSConverter.class
com\aionemu\gameserver\dataholders\CosmeticItemsData.class
com\aionemu\gameserver\network\aion\clientpackets\CM_WINDSTREAM.class
com\aionemu\gameserver\services\AtreianPassportService$SingletonHolder.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CLOSE_DIALOG.class
com\aionemu\gameserver\services\NpcShoutsService.class
com\aionemu\gameserver\services\event\Event.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_SEND_EMBLEM.class
com\aionemu\gameserver\geoEngine\collision\CollisionResults.class
com\aionemu\gameserver\services\RepurchaseService.class
com\aionemu\gameserver\spawnengine\WalkerGroupType.class
com\aionemu\gameserver\network\aion\clientpackets\CM_FRIEND_ADD$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElement.class
com\aionemu\gameserver\model\templates\spawns\SpawnSearchResult.class
com\aionemu\gameserver\model\templates\spawns\mercenaries\MercenaryRace.class
com\aionemu\gameserver\model\gameobjects\player\Equipment$1.class
com\aionemu\gameserver\model\templates\spawns\siegespawns\SiegeSpawn$SiegeRaceTemplate.class
com\aionemu\gameserver\model\gameobjects\player\npcFaction\NpcFactions$1.class
com\aionemu\gameserver\model\stats\container\SummonGameStats.class
com\aionemu\gameserver\model\gameobjects\Trap.class
com\aionemu\gameserver\services\summons\SummonsService$1.class
com\aionemu\gameserver\dao\AnnouncementsDAO$1.class
com\aionemu\gameserver\services\SurveyService$SingletonHolder.class
com\aionemu\gameserver\services\abyss\AbyssRankingCache$SingletonHolder.class
com\aionemu\gameserver\model\templates\walker\RouteVersion.class
com\aionemu\gameserver\model\templates\item\actions\ExpExtractAction$1.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_ACCOUNT_CONNECTION_INFO.class
com\aionemu\gameserver\taskmanager\tasks\ExpireTimerTask$SingletonHolder.class
com\aionemu\gameserver\ai\AIHandlerClassListener.class
com\aionemu\gameserver\model\templates\housing\HousingMoveableItem.class
com\aionemu\gameserver\model\stats\calc\functions\MaxHpFunction.class
com\aionemu\gameserver\skillengine\properties\TargetRangeAttribute.class
com\aionemu\gameserver\model\templates\siegelocation\SiegeLocationTemplate.class
com\aionemu\gameserver\dao\InGameShopLogDAO.class
com\aionemu\gameserver\services\BrokerService$SingletonHolder.class
com\aionemu\gameserver\network\chatserver\ChatServer$SingletonHolder.class
com\aionemu\gameserver\model\gameobjects\StorageObject.class
com\aionemu\gameserver\model\templates\quest\Rewards.class
com\aionemu\gameserver\model\templates\item\actions\EnchantItemAction$2.class
com\aionemu\gameserver\questEngine\handlers\AbstractQuestHandler.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\QuestVar.class
com\aionemu\gameserver\questEngine\handlers\models\ReportOnLevelUpData.class
com\aionemu\gameserver\skillengine\effect\NoDeathPenaltyEffect.class
com\aionemu\gameserver\questEngine\task\checker\DestinationChecker.class
com\aionemu\gameserver\skillengine\effect\HealOverTimeEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElementType$5.class
com\aionemu\gameserver\dao\PlayerDAO$9.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GM_SEARCH.class
com\aionemu\gameserver\model\templates\item\actions\ItemActions.class
com\aionemu\gameserver\services\drop\DropDistributionService.class
com\aionemu\gameserver\geoEngine\utils\IntMap$IntMapIterator.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\KillOperation.class
com\aionemu\gameserver\configs\main\GeoDataConfig.class
com\aionemu\gameserver\services\toypet\PetMoodService.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RECEIVE_BIDS.class
com\aionemu\gameserver\questEngine\handlers\models\QuestSkillData.class
com\aionemu\gameserver\world\container\LegionMemberContainer.class
com\aionemu\gameserver\ai\AIRequest.class
com\aionemu\gameserver\model\templates\item\actions\ExtractAction$1.class
com\aionemu\gameserver\model\items\NpcEquippedGear.class
com\aionemu\gameserver\services\MixfightService$RewardTier.class
com\aionemu\gameserver\skillengine\condition\SelfFlyingCondition.class
com\aionemu\gameserver\model\onevsone\OneVsOneMatch.class
com\aionemu\gameserver\model\gameobjects\Gatherable.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_GS_AUTH.class
com\aionemu\gameserver\skillengine\condition\WeaponCondition.class
com\aionemu\gameserver\services\worldraid\WorldRaidDeathListener.class
com\aionemu\gameserver\model\team\legion\LegionMember$1.class
com\aionemu\gameserver\model\gameobjects\Pet$1.class
com\aionemu\gameserver\model\templates\petskill\PetSkillTemplate.class
com\aionemu\gameserver\questEngine\model\ConditionOperation.class
com\aionemu\gameserver\model\templates\housing\Building.class
com\aionemu\gameserver\questEngine\handlers\models\ItemOrdersData.class
com\aionemu\gameserver\utils\cron\ThreadPoolManagerRunnableRunner.class
com\aionemu\gameserver\skillengine\properties\TargetStatusProperty.class
com\aionemu\gameserver\services\DebugService.class
com\aionemu\gameserver\world\zone\ZoneService$1.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_GS_CHARACTER.class
com\aionemu\gameserver\model\autogroup\AutoPvPFFAInstance.class
com\aionemu\gameserver\dao\InventoryDAO.class
com\aionemu\gameserver\model\templates\item\actions\CraftLearnAction.class
com\aionemu\gameserver\utils\collections\FixedElementCountSplitList$FixedElementCountListPart.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$20.class
com\aionemu\gameserver\skillengine\model\Skill$SkillMethod.class
com\aionemu\gameserver\model\templates\gather\Materials.class
com\aionemu\gameserver\model\broker\BrokerItemMask.class
com\aionemu\gameserver\controllers\attack\AggroList.class
com\aionemu\gameserver\dataholders\loadingutils\adapters\NpcEquipmentList.class
com\aionemu\gameserver\services\WarehouseService.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PLAYER_STATUS_INFO.class
com\aionemu\gameserver\model\templates\item\actions\MultiReturnAction$1.class
com\aionemu\gameserver\model\gameobjects\GroupGate.class
com\aionemu\gameserver\skillengine\effect\modifier\ActionModifier.class
com\aionemu\gameserver\model\templates\itemgroups\GatherGroup.class
com\aionemu\gameserver\dataholders\TeleLocationData.class
com\aionemu\gameserver\network\aion\clientpackets\CM_INSTANCE_INFO.class
com\aionemu\gameserver\model\broker\filter\BrokerContainsFilter.class
com\aionemu\gameserver\model\templates\zone\Points.class
com\aionemu\gameserver\controllers\observer\ActionObserver.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$19.class
com\aionemu\gameserver\skillengine\effect\HealCastorOnAttackedEffect$1.class
com\aionemu\gameserver\model\team\alliance\events\AllianceDisbandEvent.class
com\aionemu\gameserver\skillengine\model\SpellStatus.class
com\aionemu\gameserver\world\WorldType.class
com\aionemu\gameserver\ai\manager\AttackManager$1.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$5.class
com\aionemu\gameserver\model\team\group\PlayerGroupStats.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SELL_ITEM.class
com\aionemu\gameserver\services\siege\AgentSiege$1.class
com\aionemu\gameserver\geoEngine\scene\VertexBuffer.class
com\aionemu\gameserver\dataholders\loadingutils\XmlMerger.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ATTACK.class
com\aionemu\gameserver\model\broker\filter\BrokerContainsExtraFilter.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BLOCK_DEL.class
com\aionemu\gameserver\model\templates\stats\CreatureSpeeds.class
com\aionemu\gameserver\skillengine\model\DispelSlotType.class
com\aionemu\gameserver\services\EnchantService$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_UI_SETTINGS.class
com\aionemu\gameserver\ai\AIActions.class
com\aionemu\gameserver\model\instance\InstanceCoolTimeType.class
com\aionemu\gameserver\services\player\PlayerLimitService.class
com\aionemu\gameserver\questEngine\model\QuestActionType.class
com\aionemu\gameserver\services\mail\AuctionResult.class
com\aionemu\gameserver\controllers\NpcController.class
com\aionemu\gameserver\model\account\Account.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$AetherGemBiscuitGroup.class
com\aionemu\gameserver\model\templates\survey\SurveyItem.class
com\aionemu\gameserver\model\team\alliance\events\PlayerAllianceUpdateEvent.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$FeedArmorGroup.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DELETE_WAREHOUSE_ITEM.class
com\aionemu\gameserver\taskmanager\tasks\TeamMoveUpdater.class
com\aionemu\gameserver\dao\PlayerEffectsDAO.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TOWNS_LIST.class
com\aionemu\gameserver\world\WorldMapInstanceFactory.class
com\aionemu\gameserver\model\templates\item\actions\CompositionAction.class
com\aionemu\gameserver\network\aion\clientpackets\CM_ENTER_WORLD.class
com\aionemu\gameserver\services\VortexService$3.class
com\aionemu\gameserver\model\team\common\events\PlayerStopMentoringEvent.class
com\aionemu\gameserver\geoEngine\bounding\BoundingSphere.class
com\aionemu\gameserver\dao\PlayerTitleListDAO.class
com\aionemu\gameserver\model\Announcement.class
com\aionemu\gameserver\model\templates\challenge\ContributionReward.class
com\aionemu\gameserver\model\enchants\TemperingStat.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$HealthyFoodSpicyGroup.class
com\aionemu\gameserver\model\team\alliance\PlayerAllianceService.class
com\aionemu\gameserver\skillengine\properties\FirstTargetAttribute.class
com\aionemu\gameserver\model\stats\calc\functions\IStatFunction.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_HDD_BANLIST.class
com\aionemu\gameserver\model\templates\pet\PetFeedResult.class
com\aionemu\gameserver\services\event\EventService$SingletonHolder.class
com\aionemu\gameserver\services\abyss\AbyssPointsService.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_PTRANSFER_RESPONSE.class
com\aionemu\gameserver\controllers\effect\EffectController$1.class
com\aionemu\gameserver\services\item\ItemActionService$2.class
com\aionemu\gameserver\services\QuestService$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PRIVATE_STORE.class
com\aionemu\gameserver\services\ShieldService.class
com\aionemu\gameserver\custom\pvpmap\PvpMapHandler.class
com\aionemu\gameserver\model\siege\FortressLocation.class
com\aionemu\gameserver\model\templates\item\actions\TuningAction$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_QUESTIONNAIRE.class
com\aionemu\gameserver\network\loginserver\LoginServer$SingletonHolder.class
com\aionemu\gameserver\model\gameobjects\AionObject.class
com\aionemu\gameserver\utils\collections\DynamicElementCountSplitList$DynamicElementCountListPart.class
com\aionemu\gameserver\ai\manager\SkillAttackManager$1.class
com\aionemu\gameserver\configs\main\NameConfig.class
com\aionemu\gameserver\skillengine\effect\NoResurrectPenaltyEffect.class
com\aionemu\gameserver\geoEngine\scene\Mesh.class
com\aionemu\gameserver\services\CuringZoneService$1.class
com\aionemu\gameserver\model\templates\globaldrops\StringFunction.class
com\aionemu\gameserver\questEngine\task\QuestTasks.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RECONNECT_KEY.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SHOW_BRAND.class
com\aionemu\gameserver\model\stats\container\CreatureLifeStats.class
com\aionemu\gameserver\dataholders\AtreianPassportData.class
com\aionemu\gameserver\model\team\group\events\ChangeGroupLeaderEvent.class
com\aionemu\gameserver\ai\manager\WalkManager$1.class
com\aionemu\gameserver\model\templates\vortex\HomePoint.class
com\aionemu\gameserver\services\PunishmentService.class
com\aionemu\gameserver\dao\PlayerDAO$4.class
com\aionemu\gameserver\model\stats\listeners\ItemEquipmentListener$1.class
com\aionemu\gameserver\configs\main\SecurityConfig$MultiClientingRestrictionMode.class
com\aionemu\gameserver\network\aion\clientpackets\CM_REPLACE_ITEM.class
com\aionemu\gameserver\model\gameobjects\Servant.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$HealthyFoodAllGroup.class
com\aionemu\gameserver\spawnengine\WalkerFormator.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CHARACTER_LIST.class
com\aionemu\gameserver\ai\handler\AggroEventHandler$AggroNotifier.class
com\aionemu\gameserver\skillengine\effect\ResurrectPositionalEffect.class
com\aionemu\gameserver\model\gameobjects\HouseDecoration.class
com\aionemu\gameserver\model\templates\pet\PetBuff.class
com\aionemu\gameserver\model\templates\siegelocation\SiegeLegionReward.class
com\aionemu\gameserver\geoEngine\bounding\BoundingVolume.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PLAYER_REGION.class
com\aionemu\gameserver\model\base\SiegeBase.class
com\aionemu\gameserver\controllers\PlayerController$1.class
com\aionemu\gameserver\model\templates\siegelocation\SiegeReward.class
com\aionemu\gameserver\dao\PlayerDAO$2.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_ADD_MEMBER.class
com\aionemu\gameserver\services\VortexService$VortexServiceHolder.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FORTRESS_STATUS.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_EDIT.class
com\aionemu\gameserver\services\CuringZoneService.class
com\aionemu\gameserver\skillengine\condition\BackCondition.class
com\aionemu\gameserver\model\team\group\events\PlayerGroupInvite.class
com\aionemu\gameserver\model\account\Account$1.class
com\aionemu\gameserver\utils\chathandlers\ChatProcessor$SingletonHolder.class
com\aionemu\gameserver\network\aion\iteminfo\WrapInfoBlobEntry.class
com\aionemu\gameserver\model\templates\rewards\CraftRecipe.class
com\aionemu\gameserver\skillengine\model\ProvokeTarget.class
com\aionemu\gameserver\model\templates\teleport\TeleLocIdData.class
com\aionemu\gameserver\model\base\PanesterraBaseLocation.class
com\aionemu\gameserver\dataholders\BaseData.class
com\aionemu\gameserver\model\house\HouseRegistry.class
com\aionemu\gameserver\utils\chathandlers\PlayerCommand.class
com\aionemu\gameserver\model\CreatureType.class
com\aionemu\gameserver\dao\BlockListDAO.class
com\aionemu\gameserver\model\templates\item\actions\ReadAction.class
com\aionemu\gameserver\services\player\GeneralUpdateTask.class
com\aionemu\gameserver\ai\AITemplate.class
com\aionemu\gameserver\model\templates\item\actions\MultiReturnAction.class
com\aionemu\gameserver\model\templates\housing\BuildingCapabilities.class
com\aionemu\gameserver\network\aion\AionConnection.class
com\aionemu\gameserver\model\templates\item\actions\TamperingAction.class
com\aionemu\gameserver\model\autogroup\AutoPvpInstance.class
com\aionemu\gameserver\model\stats\calc\functions\MagicalAttackFunction.class
com\aionemu\gameserver\world\World.class
com\aionemu\gameserver\model\craft\Profession.class
com\aionemu\gameserver\model\gameobjects\player\PlayerSettings.class
com\aionemu\gameserver\geoEngine\scene\GLObject$Type.class
com\aionemu\gameserver\services\player\PlayerEnterWorldService.class
com\aionemu\gameserver\services\AutoGroupService.class
com\aionemu\gameserver\skillengine\effect\OneTimeBoostSkillAttackEffect$1.class
com\aionemu\gameserver\skillengine\effect\RandomMoveLocEffect.class
com\aionemu\gameserver\model\templates\npcshout\ShoutEventType.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CAPTCHA.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SUMMON_PANEL_REMOVE.class
com\aionemu\gameserver\network\aion\serverpackets\SM_BLOCK_RESPONSE.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RIDE_ROBOT.class
com\aionemu\gameserver\model\team\common\legacy\LootRuleType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CAPTCHA.class
com\aionemu\gameserver\model\templates\portal\PortalLoc.class
com\aionemu\gameserver\skillengine\effect\SummonHouseGateEffect.class
com\aionemu\gameserver\skillengine\effect\HiPassEffect.class
com\aionemu\gameserver\dao\EventDAO.class
com\aionemu\gameserver\world\WorldMapType.class
com\aionemu\gameserver\services\toypet\PetService$SingletonHolder.class
com\aionemu\gameserver\model\gameobjects\EmblemObject.class
com\aionemu\gameserver\model\templates\housing\HousingPostbox.class
com\aionemu\gameserver\dataholders\ItemSetData.class
com\aionemu\gameserver\utils\collections\DynamicServerPacketBodySplitList.class
com\aionemu\gameserver\skillengine\effect\StaggerEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PLAYER_SEARCH.class
com\aionemu\gameserver\services\GameTimeService$SingletonHolder.class
com\aionemu\gameserver\model\templates\item\Acquisition.class
com\aionemu\gameserver\skillengine\effect\DeboostHealEffect.class
com\aionemu\gameserver\model\templates\pet\PetTemplate.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MAY_LOGIN_INTO_GAME.class
com\aionemu\gameserver\network\loginserver\serverpackets\SM_ACCOUNT_TOLL_INFO.class
com\aionemu\gameserver\taskmanager\tasks\MovementNotifyTask$MoveNotifier.class
com\aionemu\gameserver\world\MapRegion.class
com\aionemu\gameserver\model\assemblednpc\AssembledNpcPart.class
com\aionemu\gameserver\services\LifeStatsRestoreService$FpReduceTask.class
com\aionemu\gameserver\model\templates\tradelist\TradeNpcType.class
com\aionemu\gameserver\skillengine\effect\BoostSkillCostEffect.class
com\aionemu\gameserver\skillengine\task\AbstractInteractionTask$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SEND_MAIL.class
com\aionemu\gameserver\services\TownService$SingletonHolder.class
com\aionemu\gameserver\controllers\observer\AttackerCriticalStatusObserver.class
com\aionemu\gameserver\model\gameobjects\player\PortalCooldown.class
com\aionemu\gameserver\model\animations\AttackHandAnimation.class
com\aionemu\gameserver\model\limiteditems\LimitedTradeNpc.class
com\aionemu\gameserver\network\aion\clientpackets\CM_FIND_GROUP.class
com\aionemu\gameserver\skillengine\effect\DamageEffect.class
com\aionemu\gameserver\dataholders\SiegeLocationData$1.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$FeedBalaurGroup.class
com\aionemu\gameserver\model\team\group\events\PlayerStartMentoringEvent.class
com\aionemu\gameserver\network\aion\clientpackets\CM_TOGGLE_SKILL_DEACTIVATE.class
com\aionemu\gameserver\dao\OldNamesDAO.class
com\aionemu\gameserver\model\stats\calc\functions\StatSetFunction.class
com\aionemu\gameserver\controllers\observer\AbstractQuestZoneObserver$1.class
com\aionemu\gameserver\model\team\TeamMember.class
com\aionemu\gameserver\model\templates\item\RandomType.class
com\aionemu\gameserver\model\instance\StageType.class
com\aionemu\gameserver\skillengine\model\Effect$2.class
com\aionemu\gameserver\instance\InstanceEngine$SingletonHolder.class
com\aionemu\gameserver\skillengine\effect\AuraEffect.class
com\aionemu\gameserver\controllers\attack\AttackUtil$1.class
com\aionemu\gameserver\skillengine\properties\TargetSpeciesAttribute.class
com\aionemu\gameserver\model\templates\LegionDominionReward.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$StinkingJunkGroup.class
com\aionemu\gameserver\network\aion\clientpackets\CM_STOP_TRAINING.class
com\aionemu\gameserver\network\aion\iteminfo\GeneralInfoBlobEntry.class
com\aionemu\gameserver\model\gameobjects\player\Rates$18.class
com\aionemu\gameserver\model\templates\item\actions\TamperingAction$1.class
com\aionemu\gameserver\skillengine\effect\RecallInstantEffect.class
com\aionemu\gameserver\skillengine\model\MotionTime.class
com\aionemu\gameserver\model\base\BaseColorType.class
com\aionemu\gameserver\model\templates\chest\KeyItem.class
com\aionemu\gameserver\model\templates\LegionDominionLocationTemplate.class
com\aionemu\gameserver\model\team\alliance\events\AssignViceCaptainEvent$AssignType.class
com\aionemu\gameserver\skillengine\effect\DPTransferEffect.class
com\aionemu\gameserver\services\DuelService$SingletonHolder.class
com\aionemu\gameserver\model\templates\item\ItemActivationTarget.class
com\aionemu\gameserver\model\gameobjects\VisibleObject.class
com\aionemu\gameserver\services\reward\AdventService.class
com\aionemu\gameserver\world\zone\ZoneAttributes.class
com\aionemu\gameserver\model\broker\filter\BrokerPlayerClassFilter.class
com\aionemu\gameserver\taskmanager\tasks\LegionDominionIntruderUpdateTask.class
com\aionemu\gameserver\skillengine\effect\DiseaseEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MAIL_SERVICE.class
com\aionemu\gameserver\network\EncryptionKeyPair.class
com\aionemu\gameserver\model\gameobjects\player\emotion\Emotion.class
com\aionemu\gameserver\network\aion\clientpackets\CM_FRIEND_ADD.class
com\aionemu\gameserver\configs\network\NetworkConfig.class
com\aionemu\gameserver\skillengine\properties\FirstTargetProperty.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MARK_FRIENDLIST.class
com\aionemu\gameserver\ai\poll\AIQuestion.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$28.class
com\aionemu\gameserver\custom\instance\neuralnetwork\PlayerModelLink.class
com\aionemu\gameserver\model\templates\npcskill\NpcSkillTemplate.class
com\aionemu\gameserver\model\autogroup\AutoGroupType.class
com\aionemu\gameserver\world\zone\NoFlyZoneInstance.class
com\aionemu\gameserver\network\aion\serverpackets\SM_BLOCK_LIST.class
com\aionemu\gameserver\questEngine\handlers\models\SkillUseData.class
com\aionemu\gameserver\skillengine\effect\CaseHealEffect.class
com\aionemu\gameserver\model\AttendType.class
com\aionemu\gameserver\model\templates\siegelocation\DoorRepairData.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ALLIANCE_MEMBER_INFO.class
com\aionemu\gameserver\services\drop\DropRegistrationService$SingletonHolder.class
com\aionemu\gameserver\skillengine\model\SkillAliasLocation.class
com\aionemu\gameserver\configs\main\CustomConfig.class
com\aionemu\gameserver\dao\PlayerRecipesDAO$2.class
com\aionemu\gameserver\geoEngine\math\Vector2f.class
com\aionemu\gameserver\skillengine\action\Actions.class
com\aionemu\gameserver\skillengine\task\AbstractCraftTask$CraftType.class
com\aionemu\gameserver\model\flypath\FlyPathType.class
com\aionemu\gameserver\network\aion\clientpackets\CM_IN_GAME_SHOP_INFO.class
com\aionemu\gameserver\ai\handler\TalkEventHandler.class
com\aionemu\gameserver\controllers\observer\AttackStatusObserver.class
com\aionemu\gameserver\taskmanager\tasks\housing\AuctionAutoFillTask.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GROUP_DATA_EXCHANGE.class
com\aionemu\gameserver\instance\InstanceHandlerClassListener.class
com\aionemu\gameserver\model\team\legion\LegionEmblem.class
com\aionemu\gameserver\model\team\alliance\events\PlayerAllianceEnteredEvent.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ITEM_USAGE_ANIMATION.class
com\aionemu\gameserver\model\skill\SkillList.class
com\aionemu\gameserver\network\aion\clientpackets\CM_OPEN_STATICDOOR.class
com\aionemu\gameserver\network\aion\serverpackets\SM_AUTO_GROUP.class
com\aionemu\gameserver\skillengine\effect\SummonSkillAreaEffect.class
com\aionemu\gameserver\dataholders\TradeListData.class
com\aionemu\gameserver\controllers\observer\AbstractCollisionObserver$CheckType.class
com\aionemu\gameserver\model\SellLimit.class
com\aionemu\gameserver\dao\BlockListDAO$4.class
com\aionemu\gameserver\utils\chathandlers\ChatCommand.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$AetherCherryGroup.class
com\aionemu\gameserver\skillengine\effect\BackDashEffect.class
com\aionemu\gameserver\services\item\ItemPurificationService.class
com\aionemu\gameserver\skillengine\effect\MPShieldEffect.class
com\aionemu\gameserver\model\templates\spawns\SpawnSpotTemplate.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DELETE_CHARACTER.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_REQUEST_KICK_ACCOUNT.class
com\aionemu\gameserver\model\templates\portal\PortalItem.class
com\aionemu\gameserver\skillengine\effect\RecallInstantEffect$1.class
com\aionemu\gameserver\services\AnnouncementService$1.class
com\aionemu\gameserver\services\mail\MailFormatter$5.class
com\aionemu\gameserver\model\base\StainedBaseLocation.class
com\aionemu\gameserver\services\DevilsMarkService$1.class
com\aionemu\gameserver\services\StigmaService$1.class
com\aionemu\gameserver\custom\instance\neuralnetwork\Sigmoid.class
com\aionemu\gameserver\model\templates\housing\HouseAddress.class
com\aionemu\gameserver\skillengine\effect\DPHealInstantEffect.class
com\aionemu\gameserver\model\gameobjects\PostboxObject.class
com\aionemu\gameserver\model\templates\siegelocation\AssaulterTemplate.class
com\aionemu\gameserver\model\event\Headhunter.class
com\aionemu\gameserver\model\stats\calc\functions\BoostCastingTimeFunction.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHAT_MESSAGE_WHISPER.class
com\aionemu\gameserver\model\templates\item\AcquisitionType.class
com\aionemu\gameserver\services\item\ItemPacketService$1.class
com\aionemu\gameserver\configs\main\EventsConfig.class
com\aionemu\gameserver\skillengine\task\CraftingTask.class
com\aionemu\gameserver\services\siege\FortressSiege.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LOOT_ITEM.class
com\aionemu\gameserver\model\templates\quest\QuestBonuses.class
com\aionemu\gameserver\model\enchants\EnchantEffect$1.class
com\aionemu\gameserver\skillengine\action\Action.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHECK_NICKNAME.class
com\aionemu\gameserver\dataholders\NpcShoutData.class
com\aionemu\gameserver\model\base\PanesterraArtifact.class
com\aionemu\gameserver\dataholders\TemperingData.class
com\aionemu\gameserver\model\templates\spawns\HouseSpawns.class
com\aionemu\gameserver\controllers\ObserveController$1.class
com\aionemu\gameserver\taskmanager\AbstractPeriodicTaskManager.class
com\aionemu\gameserver\network\aion\instanceinfo\CrucibleScoreWriter.class
com\aionemu\gameserver\controllers\observer\StanceObserver.class
com\aionemu\gameserver\model\team\group\PlayerGroupService.class
com\aionemu\gameserver\model\templates\spawns\panesterra\AhserionsFlightSpawn$AhserionStageSpawnTemplate.class
com\aionemu\gameserver\dao\EventDAO$StoredBuffData.class
com\aionemu\gameserver\ai\handler\DiedEventHandler.class
com\aionemu\gameserver\model\enchants\EnchantList.class
com\aionemu\gameserver\skillengine\effect\BleedEffect.class
com\aionemu\gameserver\dao\PlayerSettingsDAO$3.class
com\aionemu\gameserver\model\templates\recipe\ComboProduct.class
com\aionemu\gameserver\skillengine\effect\CondSkillLauncherEffect.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\DialogIdCondition.class
com\aionemu\gameserver\model\templates\item\LeftHandSlot.class
com\aionemu\gameserver\world\zone\InvasionZoneInstance.class
com\aionemu\gameserver\model\gameobjects\player\Equipment$1$1.class
com\aionemu\gameserver\controllers\movement\GlideFlag.class
com\aionemu\gameserver\model\stats\container\NpcGameStats.class
com\aionemu\gameserver\model\templates\mail\MailPartType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FORTRESS_INFO.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SECONDARY_SHOW_DECOMPOSABLE.class
com\aionemu\gameserver\geoEngine\utils\IntMap.class
com\aionemu\gameserver\geoEngine\collision\bih\BIHNode$BIHStackData.class
com\aionemu\gameserver\world\exceptions\DuplicateAionObjectException.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEGION_MODIFY_EMBLEM.class
com\aionemu\gameserver\dataholders\SpawnsData.class
com\aionemu\gameserver\services\LegionService$6.class
com\aionemu\gameserver\services\ChallengeTaskService$SingletonHolder.class
com\aionemu\gameserver\model\instance\instancescore\PvPArenaScore.class
com\aionemu\gameserver\taskmanager\tasks\MoveTaskManager.class
com\aionemu\gameserver\skillengine\effect\BindEffect.class
com\aionemu\gameserver\skillengine\effect\SummonServantEffect.class
com\aionemu\gameserver\cache\HTMLCache$1.class
com\aionemu\gameserver\utils\xml\CompressUtil.class
com\aionemu\gameserver\skillengine\effect\DispelNpcDebuffEffect.class
com\aionemu\gameserver\controllers\observer\DialogObserver.class
com\aionemu\gameserver\services\player\MultiClientingService$Identifiers.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SUMMON_MOVE.class
com\aionemu\gameserver\utils\audit\GMService.class
com\aionemu\gameserver\questEngine\model\QuestState$1.class
com\aionemu\gameserver\model\templates\world\WeatherEntry.class
com\aionemu\gameserver\model\team\legion\Legion$Announcement.class
com\aionemu\gameserver\services\CuringZoneService$1$1.class
com\aionemu\gameserver\services\summons\TrapService.class
com\aionemu\gameserver\questEngine\handlers\template\MonsterHunt.class
com\aionemu\gameserver\model\templates\challenge\ChallengeQuestTemplate.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$AetherCrystalBiscuit.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_SEND_EMBLEM_DATA.class
com\aionemu\gameserver\dataholders\ShieldData.class
com\aionemu\gameserver\services\LifeStatsRestoreService$FpRestoreTask.class
com\aionemu\gameserver\network\aion\serverpackets\SM_POSITION_SELF.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$16.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEGION_WH_KINAH.class
com\aionemu\gameserver\services\player\PlayerMailboxState.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TARGET_UPDATE.class
com\aionemu\gameserver\dataholders\ItemRestrictionCleanupData.class
com\aionemu\gameserver\model\templates\item\ExceedEnchantSkillSetType.class
com\aionemu\gameserver\geoEngine\scene\AbstractBox.class
com\aionemu\gameserver\model\gameobjects\player\npcFaction\NpcFactions$2.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEARN_RECIPE.class
com\aionemu\gameserver\model\stats\container\PlumStatEnum.class
com\aionemu\gameserver\network\aion\clientpackets\CM_REVIVE.class
com\aionemu\gameserver\geoEngine\math\Matrix3f.class
com\aionemu\gameserver\services\item\ItemChargeService.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MAC_ADDRESS.class
com\aionemu\gameserver\services\PeriodicSaveService.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET$PacketElementType$3.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EXCHANGE_CANCEL.class
com\aionemu\gameserver\dao\MailDAO$4.class
com\aionemu\gameserver\network\aion\AionClientPacket.class
com\aionemu\gameserver\model\siege\Influence$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CHARACTER_SELECT.class
com\aionemu\gameserver\skillengine\effect\BoostSkillCastingTimeEffect.class
com\aionemu\gameserver\dataholders\AIData.class
com\aionemu\gameserver\model\templates\item\actions\AssemblyItemAction.class
com\aionemu\gameserver\model\templates\teleport\TeleportType.class
com\aionemu\gameserver\skillengine\model\StigmaType.class
com\aionemu\gameserver\dataholders\KillBountyData.class
com\aionemu\gameserver\model\stats\calc\functions\StatWeaponMasteryFunction$1.class
com\aionemu\gameserver\model\templates\spawns\TemporarySpawn.class
com\aionemu\gameserver\skillengine\effect\OneTimeBoostSkillCriticalEffect$1.class
com\aionemu\gameserver\model\ingameshop\IGItem.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MOTION.class
com\aionemu\gameserver\world\zone\ZoneInstance.class
com\aionemu\gameserver\network\aion\clientpackets\CM_START_LOOT.class
com\aionemu\gameserver\network\sequrity\FloodManager$1.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$14.class
com\aionemu\gameserver\model\templates\item\ResultedItem.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_OBJECTS.class
com\aionemu\gameserver\skillengine\model\WeaponTypeWrapper$1.class
com\aionemu\gameserver\dao\PlayerNpcFactionsDAO.class
com\aionemu\gameserver\model\trade\TradeList.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CASTSPELL.class
com\aionemu\gameserver\network\aion\clientpackets\CM_READ_MAIL.class
com\aionemu\gameserver\spawnengine\StaticDoorSpawnManager.class
com\aionemu\gameserver\network\aion\clientpackets\CM_BROKER_REGISTERED.class
com\aionemu\gameserver\dao\HouseObjectCooldownsDAO.class
com\aionemu\gameserver\skillengine\effect\DispelBuffEffect.class
com\aionemu\gameserver\dataholders\FlyRingData.class
com\aionemu\gameserver\model\templates\housing\PlaceableHouseObject.class
com\aionemu\gameserver\dataholders\DataManager$SingletonHolder.class
com\aionemu\gameserver\services\FlyRingService.class
com\aionemu\gameserver\dataholders\RideData.class
com\aionemu\gameserver\geoEngine\utils\IntMap$Entry.class
com\aionemu\gameserver\model\templates\housing\HousingChair.class
com\aionemu\gameserver\services\HousingBidService$1.class
com\aionemu\gameserver\model\gameobjects\NpcObjectType.class
com\aionemu\gameserver\geoEngine\scene\CollisionData.class
com\aionemu\gameserver\controllers\ObserveController.class
com\aionemu\gameserver\network\aion\serverpackets\SM_MOVE.class
com\aionemu\gameserver\model\team\league\events\LeagueChangeLeaderEvent.class
com\aionemu\gameserver\dataholders\LegionDominionData.class
com\aionemu\gameserver\network\aion\clientpackets\CM_EMOTION.class
com\aionemu\gameserver\skillengine\condition\TargetFlyingCondition$1.class
com\aionemu\gameserver\dao\PlayerAppearanceDAO.class
com\aionemu\gameserver\dao\PlayerCooldownsDAO$2.class
com\aionemu\gameserver\geoEngine\math\Ray.class
com\aionemu\gameserver\model\gameobjects\player\RequestResponseHandler.class
com\aionemu\gameserver\model\stats\calc\functions\PDefFunction.class
com\aionemu\gameserver\services\siege\ArtifactSiege.class
com\aionemu\gameserver\model\stats\calc\functions\StatSubFunction.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\QuestConditions$1.class
com\aionemu\gameserver\world\zone\ZoneLevelService.class
com\aionemu\gameserver\dataholders\UpgradeArcadeData.class
com\aionemu\gameserver\configs\main\ThreadConfig.class
com\aionemu\gameserver\skillengine\effect\FPHealInstantEffect.class
com\aionemu\gameserver\skillengine\effect\HostileUpEffect$1.class
com\aionemu\gameserver\skillengine\effect\SlowEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MAY_QUIT.class
com\aionemu\gameserver\dao\PlayerMacrosDAO$2.class
com\aionemu\gameserver\questEngine\QuestEngine.class
com\aionemu\gameserver\skillengine\effect\AbstractAbsoluteStatEffect.class
com\aionemu\gameserver\model\templates\InstanceCooltime.class
com\aionemu\gameserver\model\account\PlayerAccountData.class
com\aionemu\gameserver\services\BaseService.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TIME_CHECK.class
com\aionemu\gameserver\services\antihack\AntiHackService.class
com\aionemu\gameserver\model\gameobjects\player\Macros.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GATHER_UPDATE.class
com\aionemu\gameserver\network\aion\clientpackets\CM_TIME_CHECK_QUIT.class
com\aionemu\gameserver\network\aion\iteminfo\ConditioningInfoBlobEntry.class
com\aionemu\gameserver\model\templates\housing\HousingPassiveItem.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GM_SHOW_LEGION_MEMBERLIST.class
com\aionemu\gameserver\dataholders\EventData.class
com\aionemu\gameserver\services\FFAService.class
com\aionemu\gameserver\services\rift\RiftManager$SingletonHolder.class
com\aionemu\gameserver\services\toypet\PetService.class
com\aionemu\gameserver\model\gameobjects\SummonedObject.class
com\aionemu\gameserver\services\VortexService.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$11.class
com\aionemu\gameserver\skillengine\effect\DeathBlowEffect.class
com\aionemu\gameserver\model\templates\item\enums\ItemGroup.class
com\aionemu\gameserver\dao\FactionPackDAO.class
com\aionemu\gameserver\model\geometry\RectangleArea.class
com\aionemu\gameserver\model\templates\quest\QuestWorkItems.class
com\aionemu\gameserver\services\toypet\PetSpawnService.class
com\aionemu\gameserver\skillengine\effect\ProvokerEffect.class
com\aionemu\gameserver\model\siege\SiegeShield.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DELETE_ITEM.class
com\aionemu\gameserver\dataholders\loadingutils\adapters\LocalDateTimeAdapter.class
com\aionemu\gameserver\model\team\PlayerTeamMember.class
com\aionemu\gameserver\skillengine\periodicaction\MpUsePeriodicAction.class
com\aionemu\gameserver\utils\stats\XPRewardEnum.class
com\aionemu\gameserver\model\templates\item\RequireSkill.class
com\aionemu\gameserver\model\templates\portal\InstanceExit.class
com\aionemu\gameserver\model\base\BaseException.class
com\aionemu\gameserver\services\AdminService.class
com\aionemu\gameserver\dao\LegionDominionDAO$4.class
com\aionemu\gameserver\skillengine\effect\DelayedSpellAttackInstantEffect.class
com\aionemu\gameserver\utils\PacketSendUtility.class
com\aionemu\gameserver\questEngine\handlers\template\MentorMonsterHunt$1.class
com\aionemu\gameserver\services\FFAService$FFAParticipant.class
com\aionemu\gameserver\network\aion\serverpackets\SM_BROKER_SERVICE.class
com\aionemu\gameserver\questEngine\handlers\template\XmlQuest.class
com\aionemu\gameserver\model\items\ItemStone$1.class
com\aionemu\gameserver\model\templates\spawns\vortexspawns\VortexSpawn$VortexStateTemplate.class
com\aionemu\gameserver\skillengine\effect\EscapeEffect.class
com\aionemu\gameserver\model\enchants\TemperingTemplateData.class
com\aionemu\gameserver\services\findgroup\FindGroupService.class
com\aionemu\gameserver\model\templates\housing\HousingEmblem.class
com\aionemu\gameserver\model\templates\item\actions\StigmaUnlockAction$1.class
com\aionemu\gameserver\services\TownService.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHALLENGE_LIST.class
com\aionemu\gameserver\skillengine\properties\MaxCountProperty$1.class
com\aionemu\gameserver\model\templates\item\actions\RideAction$1.class
com\aionemu\gameserver\questEngine\handlers\template\ReportOnLevelUp.class
com\aionemu\gameserver\model\instance\playerreward\CruciblePlayerReward.class
com\aionemu\gameserver\network\aion\clientpackets\CM_ATTACK.class
com\aionemu\gameserver\dao\LegionDAO.class
com\aionemu\gameserver\model\templates\instance_bonusatrr\InstanceBonusAttr.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PET_EMOTE.class
com\aionemu\gameserver\model\stats\calc\ReverseStat.class
com\aionemu\gameserver\model\gameobjects\player\PlayerCommonData.class
com\aionemu\gameserver\services\panesterra\PanesterraService.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEGION_SEND_EMBLEM.class
com\aionemu\gameserver\network\aion\clientpackets\CM_RECONNECT_AUTH.class
com\aionemu\gameserver\questEngine\task\checker\TargetDestinationChecker.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HOUSE_EDIT.class
com\aionemu\gameserver\network\aion\clientpackets\CM_READ_EXPRESS_MAIL.class
com\aionemu\gameserver\services\siege\Siege.class
com\aionemu\gameserver\network\chatserver\serverpackets\SM_CS_PLAYER_GAG.class
com\aionemu\gameserver\skillengine\effect\ResurrectBaseEffect.class
com\aionemu\gameserver\skillengine\model\ChargedSkill.class
com\aionemu\gameserver\services\BonusPackService.class
com\aionemu\gameserver\services\item\ItemRemodelService.class
com\aionemu\gameserver\skillengine\condition\Conditions.class
com\aionemu\gameserver\services\TribeRelationService$1.class
com\aionemu\gameserver\model\templates\item\actions\AssemblyItemAction$1.class
com\aionemu\gameserver\services\DebugService$SingletonHolder.class
com\aionemu\gameserver\model\skill\NpcSkillTemplateEntry$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUBE_UPDATE.class
com\aionemu\gameserver\model\templates\item\Stigma.class
com\aionemu\gameserver\dataholders\XMLQuests.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHARGE_ITEM.class
com\aionemu\gameserver\configs\main\CraftConfig.class
com\aionemu\gameserver\configs\schedule\RiftSchedule.class
com\aionemu\gameserver\model\summons\UnsummonType.class
com\aionemu\gameserver\geoEngine\models\GeoMap.class
com\aionemu\gameserver\model\items\storage\StorageType.class
com\aionemu\gameserver\taskmanager\AbstractIterativePeriodicTaskManager.class
com\aionemu\gameserver\network\aion\iteminfo\ShieldInfoBlobEntry.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SELECT_DECOMPOSABLE.class
com\aionemu\gameserver\network\sequrity\FloodManager$FloodFilter.class
com\aionemu\gameserver\model\geometry\Plane3D.class
com\aionemu\gameserver\skillengine\model\SkillTargetSlot.class
com\aionemu\gameserver\model\templates\mail\Header.class
com\aionemu\gameserver\skillengine\model\HealType.class
com\aionemu\gameserver\model\templates\quest\QuestExtraCategory.class
com\aionemu\gameserver\model\siege\SiegeType.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_SCRIPTS.class
com\aionemu\gameserver\model\templates\BindPointTemplate.class
com\aionemu\gameserver\services\SocialService.class
com\aionemu\gameserver\dataholders\loadingutils\XmlMerger$Metadata.class
com\aionemu\gameserver\network\aion\serverpackets\SM_HOUSE_RENDER.class
com\aionemu\gameserver\model\templates\rewards\IdLevelReward.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RECIPE_DELETE.class
com\aionemu\gameserver\services\AtreianPassportService.class
com\aionemu\gameserver\model\skill\NpcSkillList.class
com\aionemu\gameserver\custom\instance\neuralnetwork\Link.class
com\aionemu\gameserver\skillengine\model\EffectResult.class
com\aionemu\gameserver\model\templates\spawns\basespawns\BaseSpawn.class
com\aionemu\gameserver\model\stats\calc\NpcStatCalculation.class
com\aionemu\gameserver\model\templates\housing\HouseType.class
com\aionemu\gameserver\skillengine\model\DashStatus.class
com\aionemu\gameserver\skillengine\condition\TargetCondition$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SUMMON_EMOTION$1.class
com\aionemu\gameserver\model\team\common\events\PlayerLeavedEvent.class
com\aionemu\gameserver\skillengine\effect\AbsoluteStatToPCDebuffEffect.class
com\aionemu\gameserver\model\rift\RiftLocation.class
com\aionemu\gameserver\controllers\attack\AddDamageEventListener.class
com\aionemu\gameserver\dataholders\loadingutils\adapters\SpaceSeparatedBytesAdapter.class
com\aionemu\gameserver\services\reward\BonusService.class
com\aionemu\gameserver\skillengine\effect\StunEffect.class
com\aionemu\gameserver\skillengine\effect\BoostHealEffect.class
com\aionemu\gameserver\services\rift\RiftInformer$2.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$17.class
com\aionemu\gameserver\dao\PlayerDAO$PlayerAndLegionInfo.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\QuestOperation.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CLOSE_QUESTION_WINDOW.class
com\aionemu\gameserver\network\loginserver\LsServerPacket.class
com\aionemu\gameserver\network\aion\instanceinfo\HarmonyScoreWriter.class
com\aionemu\gameserver\dao\HouseBidsDAO.class
com\aionemu\gameserver\model\autogroup\AutoHarmonyInstance.class
com\aionemu\gameserver\model\templates\item\actions\Level65BoostAction$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PET$1.class
com\aionemu\gameserver\world\zone\ZoneLevelService$1.class
com\aionemu\gameserver\model\templates\zone\ZoneType.class
com\aionemu\gameserver\controllers\observer\StartMovingListener.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$2.class
com\aionemu\gameserver\dataholders\PlayerExperienceTable.class
com\aionemu\gameserver\model\geometry\SphereArea.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ABNORMAL_EFFECT.class
com\aionemu\gameserver\skillengine\condition\TargetAttribute.class
com\aionemu\gameserver\dao\PlayerPunishmentsDAO$2.class
com\aionemu\gameserver\services\player\ItemUpdateTask.class
com\aionemu\gameserver\dataholders\TownSpawnsData.class
com\aionemu\gameserver\skillengine\effect\DispelDebuffPhysicalEffect.class
com\aionemu\gameserver\model\stats\calc\functions\StatArmorMasteryFunction.class
com\aionemu\gameserver\model\gameobjects\ChairObject.class
com\aionemu\gameserver\network\aion\clientpackets\CM_GET_HOUSE_BIDS.class
com\aionemu\gameserver\network\aion\clientpackets\CM_DIALOG_SELECT.class
com\aionemu\gameserver\model\trade\Exchange.class
com\aionemu\gameserver\services\RepurchaseService$SingletonHolder.class
com\aionemu\gameserver\skillengine\effect\modifier\TargetRaceDamageModifier$1.class
com\aionemu\gameserver\questEngine\handlers\template\CraftingRewards.class
com\aionemu\gameserver\skillengine\effect\DispelEffect$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_REPURCHASE.class
com\aionemu\gameserver\skillengine\effect\SignetBurstEffect.class
com\aionemu\gameserver\dao\LegionMemberDAO$2.class
com\aionemu\gameserver\dataholders\WarehouseExpandData.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropNpcGroup.class
com\aionemu\gameserver\network\aion\clientpackets\CM_LEGION.class
com\aionemu\gameserver\model\templates\item\Improvement.class
com\aionemu\gameserver\model\templates\TitleTemplate.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups$AetherPowderBiscuitGroup.class
com\aionemu\gameserver\skillengine\effect\HealEffect.class
com\aionemu\gameserver\model\gameobjects\player\motion\MotionList.class
com\aionemu\gameserver\model\enchants\TemperingEffect.class
com\aionemu\gameserver\model\templates\stats\StatsTemplate.class
com\aionemu\gameserver\model\templates\itemgroups\ManastoneGroup.class
com\aionemu\gameserver\network\aion\serverpackets\SM_EMOTION.class
com\aionemu\gameserver\model\gameobjects\StaticDoor.class
com\aionemu\gameserver\services\mail\MailFormatter$3.class
com\aionemu\gameserver\model\templates\npcskill\NpcSkillTargetAttribute.class
com\aionemu\gameserver\model\templates\npc\NpcRank.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MACRO_CREATE.class
com\aionemu\gameserver\services\summons\SummonsService.class
com\aionemu\gameserver\model\team\group\events\PlayerConnectedEvent.class
com\aionemu\gameserver\model\road\Road.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HOUSE_TELEPORT_BACK.class
com\aionemu\gameserver\model\team\legion\LegionEmblem$1.class
com\aionemu\gameserver\services\toypet\PetFeedCalculator.class
com\aionemu\gameserver\model\gameobjects\player\Mailbox.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\operations\NpcDialogOperation.class
com\aionemu\gameserver\skillengine\effect\SubEffect.class
com\aionemu\gameserver\model\legionDominion\LegionDominionLocation.class
com\aionemu\gameserver\model\templates\itemgroups\MedalGroup.class
com\aionemu\gameserver\network\aion\clientpackets\CM_DELETE_QUEST.class
com\aionemu\gameserver\dao\CustomInstancePlayerModelEntryDAO.class
com\aionemu\gameserver\services\WorldRaidService.class
com\aionemu\gameserver\skillengine\model\Motion.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GF_WEBSHOP_TOKEN_RESPONSE.class
com\aionemu\gameserver\model\gameobjects\BrokerItem$8.class
com\aionemu\gameserver\skillengine\condition\FrontCondition.class
com\aionemu\gameserver\dataholders\PetBuffsData.class
com\aionemu\gameserver\model\templates\item\actions\PackAction.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MARK_FRIENDLIST.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\QuestVarCondition$1.class
com\aionemu\gameserver\services\siege\SiegeBossDeathListener.class
com\aionemu\gameserver\model\templates\item\ItemType.class
com\aionemu\gameserver\skillengine\effect\SummonFunctionalNpcEffect$1.class
com\aionemu\gameserver\model\templates\vortex\StartPoint.class
com\aionemu\gameserver\model\templates\instance_bonusatrr\InstancePenaltyAttr.class
com\aionemu\gameserver\utils\time\gametime\GameTime$Month.class
com\aionemu\gameserver\skillengine\effect\ProcAtkInstantEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ATTACK_STATUS$TYPE.class
com\aionemu\gameserver\services\UpgradeArcadeService$SingletonHolder.class
com\aionemu\gameserver\model\GameEngine.class
com\aionemu\gameserver\model\templates\quest\QuestKill.class
com\aionemu\gameserver\model\templates\curingzones\CuringTemplate.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HOUSE_KICK.class
com\aionemu\gameserver\model\templates\item\actions\AnimationAddAction$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_WAREHOUSE_INFO.class
com\aionemu\gameserver\skillengine\effect\HideEffect$4.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHAT_AUTH.class
com\aionemu\gameserver\skillengine\condition\LeftHandCondition$1.class
com\aionemu\gameserver\controllers\observer\AbstractCollisionObserver$1.class
com\aionemu\gameserver\model\gameobjects\HouseObject$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GATHER_ANIMATION.class
com\aionemu\gameserver\ai\GeneralAIEvent.class
com\aionemu\gameserver\model\siege\SiegeRace.class
com\aionemu\gameserver\services\mail\MailFormatter.class
com\aionemu\gameserver\services\player\PlayerLeaveWorldService.class
com\aionemu\gameserver\model\templates\spawns\vortexspawns\VortexSpawn.class
com\aionemu\gameserver\model\team\legion\Legion.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SKILL_CANCEL.class
com\aionemu\gameserver\utils\time\gametime\GameTime.class
com\aionemu\gameserver\model\templates\quest\InventoryItems.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SUMMON_USESKILL.class
com\aionemu\gameserver\services\abyss\GloryPointsService.class
com\aionemu\gameserver\services\teleport\BindPointTeleportService.class
com\aionemu\gameserver\model\templates\itemgroups\FeedGroups.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\QuestNpc.class
com\aionemu\gameserver\network\aion\serverpackets\SM_WAREHOUSE_UPDATE_ITEM.class
com\aionemu\gameserver\services\SurveyService.class
com\aionemu\gameserver\dao\PlayerSettingsDAO$1.class
com\aionemu\gameserver\geoEngine\bounding\BoundingSphere$1.class
com\aionemu\gameserver\services\siege\AgentDeathListener.class
com\aionemu\gameserver\dataholders\ZoneData.class
com\aionemu\gameserver\world\zone\FlyZoneInstance.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$FeedSoul.class
com\aionemu\gameserver\skillengine\effect\DispelDebuffEffect.class
com\aionemu\gameserver\dataholders\SkillAliasLocationData.class
com\aionemu\gameserver\services\teleport\BindPointTeleportService$Cooldown.class
com\aionemu\gameserver\network\aion\serverpackets\SM_STATS_INFO.class
com\aionemu\gameserver\ai\AIName.class
com\aionemu\gameserver\model\templates\flyring\FlyRingTemplate.class
com\aionemu\gameserver\network\aion\serverpackets\SM_VIEW_PLAYER_DETAILS.class
com\aionemu\gameserver\model\templates\hotspot\HotspotTemplate.class
com\aionemu\gameserver\skillengine\model\Effect.class
com\aionemu\gameserver\skillengine\effect\EvadeEffect.class
com\aionemu\gameserver\model\templates\quest\FinishedQuestCond.class
com\aionemu\gameserver\skillengine\effect\RideRobotEffect$1.class
com\aionemu\gameserver\skillengine\model\MotionTime$1.class
com\aionemu\gameserver\utils\annotations\AnnotationManager.class
com\aionemu\gameserver\utils\chathandlers\ConsoleCommand.class
com\aionemu\gameserver\services\DevilsMarkService.class
com\aionemu\gameserver\services\toypet\PetFeedProgress.class
com\aionemu\gameserver\services\LegionService$4.class
com\aionemu\gameserver\world\knownlist\SphereKnownList.class
com\aionemu\gameserver\dao\MailDAO$2.class
com\aionemu\gameserver\skillengine\effect\SummonGroupGateEffect.class
com\aionemu\gameserver\custom\instance\neuralnetwork\PlayerModel.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ICON_INFO.class
com\aionemu\gameserver\model\stats\calc\functions\DuplicateStatFunction.class
com\aionemu\gameserver\model\items\ItemId.class
com\aionemu\gameserver\services\rift\RiftOpenRunnable.class
com\aionemu\gameserver\model\templates\housing\PlaceLocation.class
com\aionemu\gameserver\model\templates\npc\NpcTemplate.class
com\aionemu\gameserver\services\ban\BanAction.class
com\aionemu\gameserver\network\aion\serverpackets\SM_INSTANCE_SCORE.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GAME_TIME.class
com\aionemu\gameserver\model\stats\calc\PlayerStatCalculator.class
com\aionemu\gameserver\network\loginserver\LoginServer.class
com\aionemu\gameserver\services\rift\RiftEnum.class
com\aionemu\gameserver\model\templates\item\actions\ToyPetSpawnAction$1.class
com\aionemu\gameserver\configs\ingameshop\InGameShopProperty.class
com\aionemu\gameserver\cache\HTMLCache$SingletonHolder.class
com\aionemu\gameserver\model\team\common\legacy\GroupEvent.class
com\aionemu\gameserver\dao\LegionDAO$8.class
com\aionemu\gameserver\skillengine\effect\FearEffect$FearTask.class
com\aionemu\gameserver\ai\manager\SimpleAttackManager.class
com\aionemu\gameserver\network\aion\clientpackets\CM_GROUP_DISTRIBUTION.class
com\aionemu\gameserver\model\templates\portal\PortalScroll.class
com\aionemu\gameserver\network\aion\clientpackets\CM_ATREIAN_PASSPORT.class
com\aionemu\gameserver\network\aion\clientpackets\CM_MOTION.class
com\aionemu\gameserver\dao\BlockListDAO$2.class
com\aionemu\gameserver\dataholders\WindstreamData.class
com\aionemu\gameserver\model\gameobjects\player\Rates$3.class
com\aionemu\gameserver\network\sequrity\FloodManager$Result.class
com\aionemu\gameserver\model\team\alliance\events\ChangeAllianceLootRulesEvent.class
com\aionemu\gameserver\model\templates\globaldrops\GlobalDropZones.class
com\aionemu\gameserver\model\templates\stats\ModifiersTemplate.class
com\aionemu\gameserver\model\gameobjects\player\BindPointPosition.class
com\aionemu\gameserver\skillengine\effect\MPHealEffect.class
com\aionemu\gameserver\dao\PlayerSettingsDAO$5.class
com\aionemu\gameserver\dao\AccountPassportsDAO$1.class
com\aionemu\gameserver\services\FactionPackService.class
com\aionemu\gameserver\model\templates\rewards\CraftItem.class
com\aionemu\gameserver\network\aion\ServerPacketsOpcodes.class
com\aionemu\gameserver\model\guide\Guide.class
com\aionemu\gameserver\skillengine\effect\HealCastorOnAttackedEffect.class
com\aionemu\gameserver\services\player\PlayerChatService$1.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$6.class
com\aionemu\gameserver\questEngine\model\QuestVars.class
com\aionemu\gameserver\model\siege\Assaulter.class
com\aionemu\gameserver\network\aion\clientpackets\CM_FRIEND_STATUS.class
com\aionemu\gameserver\network\aion\serverpackets\SM_NEARBY_QUESTS.class
com\aionemu\gameserver\model\instance\instancescore\PvpInstanceScore$1.class
com\aionemu\gameserver\services\RiftService.class
com\aionemu\gameserver\skillengine\effect\AlwaysResistEffect$1.class
com\aionemu\gameserver\questEngine\handlers\models\ReportToManyData.class
com\aionemu\gameserver\ai\AbstractAI$1.class
com\aionemu\gameserver\skillengine\effect\HealOverTimeEffect$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TRADE_IN_LIST.class
com\aionemu\gameserver\dao\MailDAO$6.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PET_EMOTE$1.class
com\aionemu\gameserver\model\items\ItemCooldown.class
com\aionemu\gameserver\questEngine\handlers\template\ItemOrders.class
com\aionemu\gameserver\model\gameobjects\player\BindPointPosition$1.class
com\aionemu\gameserver\skillengine\properties\TargetRelationAttribute.class
com\aionemu\gameserver\network\aion\clientpackets\CM_TIME_CHECK.class
com\aionemu\gameserver\model\curingzone\CuringObject$1.class
com\aionemu\gameserver\skillengine\effect\SkillLauncherEffect.class
com\aionemu\gameserver\dao\PlayerEmotionListDAO.class
com\aionemu\gameserver\model\gameobjects\findGroup\ServerWideGroup.class
com\aionemu\gameserver\skillengine\effect\modifier\TargetClassDamageModifier.class
com\aionemu\gameserver\dataholders\SkillTreeData.class
com\aionemu\gameserver\services\ClassChangeService.class
com\aionemu\gameserver\services\panesterra\ahserion\PanesterraTeam$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FRIEND_UPDATE.class
com\aionemu\gameserver\network\aion\serverpackets\SM_RENAME.class
com\aionemu\gameserver\network\aion\clientpackets\CM_GAMEGUARD.class
com\aionemu\gameserver\dao\PlayerMacrosDAO.class
com\aionemu\gameserver\model\templates\event\upgradearcade\ArcadeRewardItem.class
com\aionemu\gameserver\network\chatserver\ChatServerConnection$State.class
com\aionemu\gameserver\skillengine\condition\SkillChargeCondition.class
com\aionemu\gameserver\utils\stats\StatFunctions$1.class
com\aionemu\gameserver\network\loginserver\LsClientPacketFactory.class
com\aionemu\gameserver\controllers\effect\PlayerEffectController.class
com\aionemu\gameserver\dao\PlayerEffectsDAO$1.class
com\aionemu\gameserver\skillengine\effect\MoveBehindEffect.class
com\aionemu\gameserver\services\teleport\TeleportService$1.class
com\aionemu\gameserver\model\templates\QuestTemplate.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GROUP_MEMBER_INFO$1.class
com\aionemu\gameserver\model\gameobjects\player\Rates$7.class
com\aionemu\gameserver\network\aion\clientpackets\CM_UPGRADE_ARCADE.class
com\aionemu\gameserver\model\templates\rewards\RewardEntryItem.class
com\aionemu\gameserver\dao\FriendListDAO$2.class
com\aionemu\gameserver\services\BrokerService$BrokerPeriodicTaskManager.class
com\aionemu\gameserver\dataholders\MotionData.class
com\aionemu\gameserver\services\SurveyService$TaskUpdate.class
com\aionemu\gameserver\dao\PlayerPasskeyDAO.class
com\aionemu\gameserver\model\templates\rewards\FoodItem.class
com\aionemu\gameserver\skillengine\effect\SignetEffect.class
com\aionemu\gameserver\network\aion\clientpackets\CM_HEADING_UPDATE.class
com\aionemu\gameserver\model\templates\challenge\ChallengeReward.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ABYSS_RANKING_LEGIONS.class
com\aionemu\gameserver\dataholders\TribeRelationsData.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$FeedExclude.class
com\aionemu\gameserver\model\gameobjects\PassiveObject.class
com\aionemu\gameserver\services\player\PlayerService.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ALLIANCE_READY_CHECK.class
com\aionemu\gameserver\model\geometry\CylinderArea.class
com\aionemu\gameserver\services\DialogService$2.class
com\aionemu\gameserver\skillengine\effect\AbnormalState.class
com\aionemu\gameserver\model\gameobjects\player\CustomPlayerState.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DELETE.class
com\aionemu\gameserver\model\templates\spawns\SpawnType.class
com\aionemu\gameserver\model\templates\worldraid\MarkerSpot.class
com\aionemu\gameserver\network\aion\iteminfo\ItemInfoBlob$ItemBlobType$8.class
com\aionemu\gameserver\utils\ThreadPoolManager$SingletonHolder.class
com\aionemu\gameserver\model\templates\spawns\panesterra\AhserionsFlightSpawn.class
com\aionemu\gameserver\dao\PlayerPunishmentsDAO.class
com\aionemu\gameserver\skillengine\model\DispelCategoryType.class
com\aionemu\gameserver\network\aion\clientpackets\CM_QUIT.class
com\aionemu\gameserver\model\templates\itemset\ItemSetTemplate.class
com\aionemu\gameserver\questEngine\QuestEngine$SingletonHolder.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SHOW_FRIENDLIST.class
com\aionemu\gameserver\skillengine\action\HpUseAction.class
com\aionemu\gameserver\skillengine\effect\RideRobotEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FRIEND_LIST.class
com\aionemu\gameserver\dao\SiegeDAO.class
com\aionemu\gameserver\model\templates\spawns\Spawn.class
com\aionemu\gameserver\skillengine\effect\PulledEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ASCENSION_MORPH.class
com\aionemu\gameserver\custom\pvpmap\PvpMapService.class
com\aionemu\gameserver\model\instance\playerreward\PvPArenaPlayerReward.class
com\aionemu\gameserver\world\zone\ZoneName.class
com\aionemu\gameserver\skillengine\effect\SpellAttackEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_PRIVATE_STORE_NAME.class
com\aionemu\gameserver\network\aion\serverpackets\SM_FIND_GROUP.class
com\aionemu\gameserver\services\player\PlayerLimitService$SingletonHolder.class
com\aionemu\gameserver\dao\LegionDAO$6.class
com\aionemu\gameserver\model\templates\npc\MassiveLoot.class
com\aionemu\gameserver\geoEngine\scene\Spatial.class
com\aionemu\gameserver\skillengine\model\ProvokeType.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$FeedBone.class
com\aionemu\gameserver\services\NpcShoutsService$NpcShoutTask.class
com\aionemu\gameserver\model\autogroup\AutoGroupType$33.class
com\aionemu\gameserver\services\WeatherService.class
com\aionemu\gameserver\services\trade\PricesService$1.class
com\aionemu\gameserver\skillengine\effect\DelayedFpAtkInstantEffect$1.class
com\aionemu\gameserver\model\templates\itemgroups\FeedEntries$PoppySnack.class
com\aionemu\gameserver\skillengine\effect\HideEffect$2.class
com\aionemu\gameserver\model\templates\siegelocation\DoorRepairStone.class
com\aionemu\gameserver\services\drop\DropService$TempTradeDropPredicate.class
com\aionemu\gameserver\questEngine\handlers\AbstractQuestHandler$1.class
com\aionemu\gameserver\services\ShieldService$SingletonHolder.class
com\aionemu\gameserver\skillengine\effect\RootEffect$1.class
com\aionemu\gameserver\instance\handlers\InstanceID.class
com\aionemu\gameserver\dataholders\SiegeLocationData.class
com\aionemu\gameserver\model\templates\item\actions\ApExtractAction$1.class
com\aionemu\gameserver\dao\PlayerDAO$10.class
com\aionemu\gameserver\model\gameobjects\Kisk.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\NpcIdCondition.class
com\aionemu\gameserver\model\gameobjects\player\Rates$5.class
com\aionemu\gameserver\network\aion\serverpackets\SM_IN_GAME_SHOP_CATEGORY_LIST.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ENTER_WORLD_CHECK$Msg.class
com\aionemu\gameserver\dao\LegionDAO$4.class
com\aionemu\gameserver\model\templates\item\actions\ReadAction$1.class
com\aionemu\gameserver\dao\BrokerDAO$4.class
com\aionemu\gameserver\utils\collections\Predicates.class
com\aionemu\gameserver\skillengine\properties\TargetRangeProperty.class
com\aionemu\gameserver\model\templates\windstreams\WindstreamTemplate.class
com\aionemu\gameserver\dataholders\ItemGroupsData.class
com\aionemu\gameserver\network\aion\serverpackets\SM_SIEGE_LOCATION_INFO.class
com\aionemu\gameserver\skillengine\effect\NoReduceSpellATKInstantEffect.class
com\aionemu\gameserver\model\broker\BrokerMessages.class
com\aionemu\gameserver\model\items\IdianStone$1.class
com\aionemu\gameserver\model\templates\itemgroups\MedicineGroup.class
com\aionemu\gameserver\dao\PlayerPetsDAO.class
com\aionemu\gameserver\model\event\ArcadeProgress.class
com\aionemu\gameserver\services\LimitedItemTradeService$SingletonHolder.class
com\aionemu\gameserver\skillengine\condition\OnFlyCondition.class
com\aionemu\gameserver\geoEngine\GeoWorldLoader.class
com\aionemu\gameserver\model\gameobjects\player\Rates$16.class
com\aionemu\gameserver\dataholders\loadingutils\StaticDataListener.class
com\aionemu\gameserver\skillengine\action\ItemUseAction.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_INFO.class
com\aionemu\gameserver\network\aion\serverpackets\SM_AFTER_SIEGE_LOCINFO_475.class
com\aionemu\gameserver\model\account\CharacterPasskey$ConnectType.class
com\aionemu\gameserver\dao\LegionDAO$2.class
com\aionemu\gameserver\model\gameobjects\player\AbyssRank.class
com\aionemu\gameserver\configs\main\DropConfig.class
com\aionemu\gameserver\dao\BrokerDAO$2.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GROUP_MEMBER_INFO.class
com\aionemu\gameserver\model\team\league\events\LeagueLootRulesChangeEvent.class
com\aionemu\gameserver\skillengine\properties\TargetRangeProperty$1.class
com\aionemu\gameserver\model\team\alliance\PlayerAllianceMember.class
com\aionemu\gameserver\skillengine\effect\DispelDebuffMentalEffect.class
com\aionemu\gameserver\network\loginserver\clientpackets\CM_GS_AUTH_RESPONSE.class
com\aionemu\gameserver\model\gameobjects\player\Rates$9.class
com\aionemu\gameserver\model\team\common\events\TeamKinahDistributionEvent.class
com\aionemu\gameserver\model\templates\item\actions\Level65BoostAction.class
com\aionemu\gameserver\ai\AILogger.class
com\aionemu\gameserver\model\gameobjects\player\Equipment.class
com\aionemu\gameserver\ai\manager\AttackManager.class
com\aionemu\gameserver\model\templates\pet\FoodType.class
com\aionemu\gameserver\model\templates\event\Buff$BuffMapType.class
com\aionemu\gameserver\model\team\alliance\events\PlayerAllianceUpdateEvent$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_DIE.class
com\aionemu\gameserver\model\team\league\events\LeagueDisbandEvent.class
com\aionemu\gameserver\controllers\observer\AttackShieldObserver$1.class
com\aionemu\gameserver\network\aion\clientpackets\CM_PLAY_MOVIE_END.class
com\aionemu\gameserver\network\aion\serverpackets\SM_LEGION_UPDATE_MEMBER.class
com\aionemu\gameserver\utils\PositionUtil.class
com\aionemu\gameserver\model\base\StainedBase.class
com\aionemu\gameserver\model\stats\calc\functions\StatFunctionProxy.class
com\aionemu\gameserver\model\team\alliance\events\PlayerAllianceInvite.class
com\aionemu\gameserver\model\gameobjects\player\Rates$10.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHAT_MESSAGE_PUBLIC$1.class
com\aionemu\gameserver\services\NameRestrictionService.class
com\aionemu\gameserver\services\PeriodicSaveService$SingletonHolder.class
com\aionemu\gameserver\network\aion\serverpackets\SM_GM_SHOW_PLAYER_SKILLS.class
com\aionemu\gameserver\services\siege\AgentSiege.class
com\aionemu\gameserver\world\zone\ZoneUpdateService.class
com\aionemu\gameserver\services\ChallengeTaskService$1.class
com\aionemu\gameserver\skillengine\effect\AlwaysNoResistEffect.class
com\aionemu\gameserver\questEngine\task\FollowingNpcCheckTask.class
com\aionemu\gameserver\model\siege\AgentLocation.class
com\aionemu\gameserver\model\gameobjects\BrokerItem$2.class
com\aionemu\gameserver\network\loginserver\LsClientPacketFactory$PacketInfo.class
com\aionemu\gameserver\model\gameobjects\player\Rates$12.class
com\aionemu\gameserver\model\team\common\events\TeamCommand.class
com\aionemu\gameserver\services\siege\Assault.class
com\aionemu\gameserver\model\ingameshop\IGRequest.class
com\aionemu\gameserver\skillengine\effect\StatupEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_ATREIAN_PASSPORT.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SHOW_BRAND.class
com\aionemu\gameserver\services\mail\SystemMailService.class
com\aionemu\gameserver\model\enchants\EnchantTemplateData.class
com\aionemu\gameserver\geoEngine\collision\CollisionResult.class
com\aionemu\gameserver\skillengine\effect\BuffSleepEffect.class
com\aionemu\gameserver\instance\handlers\InstanceHandler.class
com\aionemu\gameserver\network\aion\iteminfo\ArmorInfoBlobEntry.class
com\aionemu\gameserver\model\templates\itemgroups\ItemGroupIndex.class
com\aionemu\gameserver\model\house\HouseBids.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\conditions\PcInventoryCondition.class
com\aionemu\gameserver\dao\ChallengeTasksDAO$1.class
com\aionemu\gameserver\model\templates\mail\MailPart.class
com\aionemu\gameserver\questEngine\handlers\models\xmlQuest\QuestDialog.class
com\aionemu\gameserver\skillengine\effect\OneTimeBoostHealEffect.class
com\aionemu\gameserver\services\panesterra\PanesterraService$1.class
com\aionemu\gameserver\skillengine\model\FlyingRestriction.class
com\aionemu\gameserver\geoEngine\scene\mesh\IndexBuffer.class
com\aionemu\gameserver\network\aion\serverpackets\SM_TOLL_INFO.class
com\aionemu\gameserver\model\team\alliance\events\PlayerDisconnectedEvent.class
com\aionemu\gameserver\model\templates\goods\GoodsList$Item.class
com\aionemu\gameserver\dao\PlayerPunishmentsDAO$4.class
com\aionemu\gameserver\model\gameobjects\player\Rates$14.class
com\aionemu\gameserver\model\items\IdianStone.class
com\aionemu\gameserver\model\gameobjects\BrokerItem$4.class
com\aionemu\gameserver\network\aion\clientpackets\CM_SHOW_MAP.class
com\aionemu\gameserver\network\aion\clientpackets\CM_CHAT_PLAYER_INFO.class
com\aionemu\gameserver\world\zone\handler\ZoneHandlerClassListener.class
com\aionemu\gameserver\controllers\PlayerController.class
com\aionemu\gameserver\model\team\league\events\LeagueJoinEvent.class
com\aionemu\gameserver\services\GameTimeService.class
com\aionemu\gameserver\skillengine\effect\SkillXPBoostEffect.class
com\aionemu\gameserver\network\aion\serverpackets\SM_EXCHANGE_ADD_ITEM.class
com\aionemu\gameserver\utils\Util.class
com\aionemu\gameserver\ai\manager\SkillAttackManager.class
com\aionemu\gameserver\configs\Config.class
com\aionemu\gameserver\model\stats\calc\StatCapUtil$1.class
com\aionemu\gameserver\network\aion\serverpackets\SM_CUSTOM_PACKET.class
com\aionemu\gameserver\spawnengine\VisibleObjectSpawner.class
