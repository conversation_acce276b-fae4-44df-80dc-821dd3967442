package com.aionemu.gameserver.instance.handlers;

import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * Recharger Instance Handler - Creates clean instances without monsters
 * 
 * This handler ensures that Recharger instances are clean sanctuaries
 * without any of the original dungeon monsters or NPCs.
 * Only the healing NPC will be spawned by the RechargerService.
 * 
 * <AUTHOR>
 */
public class RechargerInstanceHandler extends GeneralInstanceHandler {

    public RechargerInstanceHandler(WorldMapInstance instance) {
        super(instance);
    }

    @Override
    public void onInstanceCreate() {
        // Don't call super.onInstanceCreate() to prevent spawning original monsters
        // The RechargerService will handle spawning only the healing NPC
    }

    @Override
    public void onPlayerLogin(Player player) {
        // Allow normal player login handling
        super.onPlayerLogin(player);
    }

    @Override
    public void onPlayerLogOut(Player player) {
        // Allow normal player logout handling
        super.onPlayerLogOut(player);
    }

    @Override
    public void onEnterInstance(Player player) {
        // Allow normal enter handling
        super.onEnterInstance(player);
    }

    @Override
    public void onLeaveInstance(Player player) {
        // Allow normal leave handling
        super.onLeaveInstance(player);
    }
}
